(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[351],{8199:function(e,t){"use strict";var s,n,r,o;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{ACTION_FAST_REFRESH:function(){return d},ACTION_NAVIGATE:function(){return a},ACTION_PREFETCH:function(){return u},ACTION_REFRESH:function(){return i},ACTION_RESTORE:function(){return l},ACTION_SERVER_ACTION:function(){return h},ACTION_SERVER_PATCH:function(){return c},PrefetchCacheEntryStatus:function(){return n},PrefetchKind:function(){return s},isThenable:function(){return p}});let i="refresh",a="navigate",l="restore",c="server-patch",u="prefetch",d="fast-refresh",h="server-action";function p(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(r=s||(s={})).AUTO="auto",r.FULL="full",r.TEMPORARY="temporary",(o=n||(n={})).fresh="fresh",o.reusable="reusable",o.expired="expired",o.stale="stale",("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7195:function(e,t,s){"use strict";function n(e,t,s,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),s(8337),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8342:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return j}});let n=s(8754),r=s(5893),o=n._(s(7294)),i=s(6075),a=s(3955),l=s(8041),c=s(9903),u=s(5490),d=s(1928),h=s(257),p=s(4229),m=s(7195),x=s(9470),_=s(8199),f=new Set;function g(e,t,s,n,r,o){if(o||(0,a.isLocalURL)(t)){if(!n.bypassPrefetchedCheck){let r=t+"%"+s+"%"+(void 0!==n.locale?n.locale:"locale"in e?e.locale:void 0);if(f.has(r))return;f.add(r)}(async()=>o?e.prefetch(t,r):e.prefetch(t,s,n))().catch(e=>{})}}function y(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}let j=o.default.forwardRef(function(e,t){let s,n;let{href:l,as:f,children:j,prefetch:k=null,passHref:v,replace:L,shallow:b,scroll:N,locale:I,onClick:w,onMouseEnter:C,onTouchStart:T,legacyBehavior:A=!1,...P}=e;s=j,A&&("string"==typeof s||"number"==typeof s)&&(s=(0,r.jsx)("a",{children:s}));let B=o.default.useContext(d.RouterContext),S=o.default.useContext(h.AppRouterContext),H=null!=B?B:S,D=!B,z=!1!==k,M=null===k?_.PrefetchKind.AUTO:_.PrefetchKind.FULL,{href:E,as:R}=o.default.useMemo(()=>{if(!B){let e=y(l);return{href:e,as:f?y(f):e}}let[e,t]=(0,i.resolveHref)(B,l,!0);return{href:e,as:f?(0,i.resolveHref)(B,f):t||e}},[B,l,f]),G=o.default.useRef(E),O=o.default.useRef(R);A&&(n=o.default.Children.only(s));let W=A?n&&"object"==typeof n&&n.ref:t,[F,U,V]=(0,p.useIntersection)({rootMargin:"200px"}),Z=o.default.useCallback(e=>{(O.current!==R||G.current!==E)&&(V(),O.current=R,G.current=E),F(e),W&&("function"==typeof W?W(e):"object"==typeof W&&(W.current=e))},[R,W,E,V,F]);o.default.useEffect(()=>{H&&U&&z&&g(H,E,R,{locale:I},{kind:M},D)},[R,E,U,I,z,null==B?void 0:B.locale,H,D,M]);let q={ref:Z,onClick(e){A||"function"!=typeof w||w(e),A&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),H&&!e.defaultPrevented&&function(e,t,s,n,r,i,l,c,u){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!u&&!(0,a.isLocalURL)(s)))return;e.preventDefault();let h=()=>{let e=null==l||l;"beforePopState"in t?t[r?"replace":"push"](s,n,{shallow:i,locale:c,scroll:e}):t[r?"replace":"push"](n||s,{scroll:e})};u?o.default.startTransition(h):h()}(e,H,E,R,L,b,N,I,D)},onMouseEnter(e){A||"function"!=typeof C||C(e),A&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),H&&(z||!D)&&g(H,E,R,{locale:I,priority:!0,bypassPrefetchedCheck:!0},{kind:M},D)},onTouchStart:function(e){A||"function"!=typeof T||T(e),A&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),H&&(z||!D)&&g(H,E,R,{locale:I,priority:!0,bypassPrefetchedCheck:!0},{kind:M},D)}};if((0,c.isAbsoluteUrl)(R))q.href=R;else if(!A||v||"a"===n.type&&!("href"in n.props)){let e=void 0!==I?I:null==B?void 0:B.locale,t=(null==B?void 0:B.isLocaleDomain)&&(0,m.getDomainLocale)(R,e,null==B?void 0:B.locales,null==B?void 0:B.domainLocales);q.href=t||(0,x.addBasePath)((0,u.addLocale)(R,e,null==B?void 0:B.defaultLocale))}return A?o.default.cloneElement(n,q):(0,r.jsx)("a",{...P,...q,children:s})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4229:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return l}});let n=s(7294),r=s(4474),o="function"==typeof IntersectionObserver,i=new Map,a=[];function l(e){let{rootRef:t,rootMargin:s,disabled:l}=e,c=l||!o,[u,d]=(0,n.useState)(!1),h=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{h.current=e},[]);return(0,n.useEffect)(()=>{if(o){if(c||u)return;let e=h.current;if(e&&e.tagName)return function(e,t,s){let{id:n,observer:r,elements:o}=function(e){let t;let s={root:e.root||null,margin:e.rootMargin||""},n=a.find(e=>e.root===s.root&&e.margin===s.margin);if(n&&(t=i.get(n)))return t;let r=new Map;return t={id:s,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=r.get(e.target),s=e.isIntersecting||e.intersectionRatio>0;t&&s&&t(s)})},e),elements:r},a.push(s),i.set(s,t),t}(s);return o.set(e,t),r.observe(e),function(){if(o.delete(e),r.unobserve(e),0===o.size){r.disconnect(),i.delete(n);let e=a.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&a.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:s})}else if(!u){let e=(0,r.requestIdleCallback)(()=>d(!0));return()=>(0,r.cancelIdleCallback)(e)}},[c,s,t,u,h.current]),[p,u,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1616:function(e,t,s){"use strict";var n=s(5893),r=s(7294),o=s(6268),i=s.n(o);t.Z=e=>{let{onProcess:t,isLoading:s,disabled:o,inputText:a,className:l=""}=e,[c,u]=(0,r.useState)(!1),d=o||s||!a.trim();return(0,n.jsxs)("div",{className:i().buttonContainer,children:[(0,n.jsxs)("button",{className:"".concat(i().processButton," ").concat(s?i().loading:""," ").concat(d?i().disabled:""," ").concat(l),onClick:()=>{!o&&!s&&a.trim()&&t()},disabled:d,onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),children:[(0,n.jsx)("div",{className:i().buttonContent,children:s?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:i().loadingSpinner}),(0,n.jsx)("span",{className:i().buttonText,children:"Processing..."})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("svg",{className:i().buttonIcon,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M12 2L2 7l10 5 10-5-10-5z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M2 17l10 5 10-5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M2 12l10 5 10-5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,n.jsx)("span",{className:i().buttonText,children:"Humanize Text"})]})}),(0,n.jsx)("div",{className:"".concat(i().buttonBackground," ").concat(c?i().hovered:"")}),!d&&(0,n.jsx)("div",{className:i().rippleContainer,children:(0,n.jsx)("div",{className:i().ripple})})]}),(0,n.jsxs)("div",{className:i().statusContainer,children:[s&&(0,n.jsxs)("div",{className:i().statusItem,children:[(0,n.jsx)("div",{className:i().statusDot}),(0,n.jsx)("span",{className:i().statusText,children:"AI is processing your text..."})]}),!a.trim()&&!s&&(0,n.jsxs)("div",{className:i().statusItem,children:[(0,n.jsxs)("svg",{className:i().statusIcon,width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"12",y1:"8",x2:"12",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16",stroke:"currentColor",strokeWidth:"2"})]}),(0,n.jsx)("span",{className:i().statusText,children:"Enter text to get started"})]}),a.trim()&&!s&&(0,n.jsxs)("div",{className:i().statusItem,children:[(0,n.jsx)("svg",{className:i().statusIcon,width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:(0,n.jsx)("polyline",{points:"20,6 9,17 4,12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),(0,n.jsx)("span",{className:i().statusText,children:"Ready to process"})]})]}),s&&(0,n.jsxs)("div",{className:i().stepsContainer,children:[(0,n.jsxs)("div",{className:i().step,children:[(0,n.jsx)("div",{className:"".concat(i().stepDot," ").concat(i().active)}),(0,n.jsx)("span",{className:i().stepText,children:"Analyzing text"})]}),(0,n.jsxs)("div",{className:i().step,children:[(0,n.jsx)("div",{className:"".concat(i().stepDot," ").concat(i().active)}),(0,n.jsx)("span",{className:i().stepText,children:"Applying modifications"})]}),(0,n.jsxs)("div",{className:i().step,children:[(0,n.jsx)("div",{className:"".concat(i().stepDot," ").concat(i().active)}),(0,n.jsx)("span",{className:i().stepText,children:"Optimizing output"})]})]})]})}},5337:function(e,t,s){"use strict";var n=s(5893);s(7294);var r=s(7205),o=s.n(r);t.Z=e=>{let{outputText:t,detectionResult:s,isLoading:r}=e;if(r)return(0,n.jsx)("div",{className:o().resultsDisplay,children:(0,n.jsxs)("div",{className:o().loadingContainer,children:[(0,n.jsx)("div",{className:o().loadingSpinner}),(0,n.jsx)("p",{children:"Analyzing text..."})]})});if(!s&&!t)return null;let i="";if(null==s?void 0:s.status){let e=s.status.toLowerCase();e.includes("human")||e.includes("skipped")?i=o().statusHuman:e.includes("ai")||e.includes("potentially")?i=o().statusAI:e.includes("mixed")&&(i=o().statusMixed)}return(null==s?void 0:s.error)&&(null==s?void 0:s.message)?(0,n.jsxs)("div",{className:o().resultsDisplay,children:[(0,n.jsxs)("div",{className:o().header,children:[(0,n.jsxs)("svg",{className:o().icon,width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor",strokeWidth:"2"})]}),(0,n.jsx)("h4",{children:"AI Detection Check"})]}),(0,n.jsxs)("div",{className:o().content,children:[(0,n.jsxs)("p",{className:o().errorMessage,children:[(0,n.jsx)("strong",{children:"Error:"})," ",s.message]}),s.status&&(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Status:"}),(0,n.jsx)("span",{className:i||"",children:s.status})]})]})]}):(0,n.jsxs)("div",{className:o().resultsDisplay,children:[(0,n.jsxs)("div",{className:o().header,children:[(0,n.jsxs)("svg",{className:o().icon,width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M9 12l2 2 4-4",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"})]}),(0,n.jsx)("h4",{children:"AI Detection Results"})]}),(0,n.jsxs)("div",{className:o().content,children:[(null==s?void 0:s.status)&&(0,n.jsx)("div",{className:o().statusSection,children:(0,n.jsxs)("div",{className:o().statusItem,children:[(0,n.jsx)("strong",{children:"Detection Status:"}),(0,n.jsx)("span",{className:"".concat(o().statusBadge," ").concat(i||""),children:s.status})]})}),(null==s?void 0:s.score)!==null&&(null==s?void 0:s.score)!==void 0&&(0,n.jsx)("div",{className:o().scoreSection,children:(0,n.jsxs)("div",{className:o().scoreItem,children:[(0,n.jsx)("strong",{children:"AI Probability:"}),(0,n.jsxs)("div",{className:o().scoreDisplay,children:[(0,n.jsxs)("span",{className:o().scoreValue,children:[(100*s.score).toFixed(1),"%"]}),(0,n.jsx)("div",{className:o().scoreBar,children:(0,n.jsx)("div",{className:o().scoreProgress,style:{width:"".concat(100*s.score,"%")}})})]})]})}),(null==s?void 0:s.message)&&(0,n.jsxs)("div",{className:o().messageSection,children:[(0,n.jsx)("strong",{children:"Details:"}),(0,n.jsx)("p",{className:o().message,children:s.message})]}),t&&(0,n.jsx)("div",{className:o().summary,children:(0,n.jsx)("p",{className:o().summaryText,children:"✅ Text processing completed successfully. Your content has been optimized for human-like characteristics."})})]})]})}},2377:function(e,t,s){"use strict";var n=s(5893),r=s(7294),o=s(3709),i=s.n(o);t.Z=e=>{let{inputText:t,onInputChange:s,outputText:o,isLoading:a,onCopy:l,onDownload:c}=e,[u,d]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),_=(0,r.useRef)(null),f=(0,r.useRef)(null),g=async(e,t)=>{try{await navigator.clipboard.writeText(e),x(t),setTimeout(()=>x(!1),2e3),l&&l(e,t)}catch(e){console.error("Failed to copy text: ",e)}},y=e=>e.trim()?e.trim().split(/\s+/).length:0;return(0,n.jsxs)("div",{className:i().editorContainer,children:[(0,n.jsxs)("div",{className:i().editorSection,children:[(0,n.jsxs)("div",{className:i().editorHeader,children:[(0,n.jsxs)("div",{className:i().headerLeft,children:[(0,n.jsxs)("h3",{className:i().editorTitle,children:[(0,n.jsxs)("svg",{className:i().titleIcon,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("polyline",{points:"14,2 14,8 20,8",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("polyline",{points:"10,9 9,9 8,9",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Original Text"]}),(0,n.jsxs)("div",{className:i().stats,children:[(0,n.jsxs)("span",{className:i().stat,children:[y(t)," words"]}),(0,n.jsxs)("span",{className:i().stat,children:[t.length," characters"]})]})]}),(0,n.jsx)("div",{className:i().headerActions,children:t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{className:i().actionButton,onClick:()=>g(t,"input"),title:"Copy original text",children:"input"===m?(0,n.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:(0,n.jsx)("polyline",{points:"20,6 9,17 4,12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}):(0,n.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,n.jsx)("button",{className:i().actionButton,onClick:()=>{s&&s({target:{value:""}})},title:"Clear text",children:(0,n.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("polyline",{points:"3,6 5,6 21,6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})})]})})]}),(0,n.jsxs)("div",{className:"".concat(i().editorWrapper," ").concat(u?i().focused:""),children:[(0,n.jsx)("textarea",{ref:_,className:i().textarea,value:t,onChange:s,onFocus:()=>d(!0),onBlur:()=>d(!1),placeholder:"Paste your AI-generated text here to make it more human-like...",disabled:a}),a&&(0,n.jsx)("div",{className:i().loadingOverlay,children:(0,n.jsx)("div",{className:i().loadingSpinner})})]})]}),(0,n.jsx)("div",{className:i().separator,children:(0,n.jsx)("div",{className:i().arrowContainer,children:(0,n.jsxs)("svg",{className:i().arrow,width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("line",{x1:"5",y1:"12",x2:"19",y2:"12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("polyline",{points:"12,5 19,12 12,19",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})})}),(0,n.jsxs)("div",{className:i().editorSection,children:[(0,n.jsxs)("div",{className:i().editorHeader,children:[(0,n.jsxs)("div",{className:i().headerLeft,children:[(0,n.jsxs)("h3",{className:i().editorTitle,children:[(0,n.jsxs)("svg",{className:i().titleIcon,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("polyline",{points:"14,2 14,8 20,8",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("circle",{cx:"11",cy:"14",r:"2",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("path",{d:"M21 15c-1-1-3-1-3-1s-2 0-3 1",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Humanized Text"]}),(0,n.jsxs)("div",{className:i().stats,children:[(0,n.jsxs)("span",{className:i().stat,children:[y(o)," words"]}),(0,n.jsxs)("span",{className:i().stat,children:[o.length," characters"]})]})]}),(0,n.jsx)("div",{className:i().headerActions,children:o&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{className:i().actionButton,onClick:()=>g(o,"output"),title:"Copy humanized text",children:"output"===m?(0,n.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:(0,n.jsx)("polyline",{points:"20,6 9,17 4,12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}):(0,n.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,n.jsx)("button",{className:i().actionButton,onClick:()=>c&&c(o),title:"Download as text file",children:(0,n.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("polyline",{points:"7,10 12,15 17,10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("line",{x1:"12",y1:"15",x2:"12",y2:"3",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})})]})})]}),(0,n.jsxs)("div",{className:"".concat(i().editorWrapper," ").concat(h?i().focused:""),children:[(0,n.jsx)("textarea",{ref:f,className:"".concat(i().textarea," ").concat(i().textareaOutput),value:o,onFocus:()=>p(!0),onBlur:()=>p(!1),placeholder:"Your humanized text will appear here...",readOnly:!0}),!o&&!a&&(0,n.jsxs)("div",{className:i().emptyState,children:[(0,n.jsxs)("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",className:i().emptyIcon,children:[(0,n.jsx)("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("polyline",{points:"14,2 14,8 20,8",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("line",{x1:"16",y1:"13",x2:"8",y2:"13",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("line",{x1:"16",y1:"17",x2:"8",y2:"17",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,n.jsx)("p",{children:'Enter text and click "Humanize Text" to see results'})]})]})]})]})}},6993:function(e,t,s){"use strict";s.d(t,{Z:function(){return _}});var n=s(5893);s(7294);var r=s(1664),o=s.n(r),i=s(9559),a=s.n(i),l=s(3299),c=s(500),u=s.n(c);function d(){let{data:e,status:t}=(0,l.useSession)();if("loading"===t)return(0,n.jsx)("div",{className:u().loading,children:"Loading..."});if(e){let t=e.user.name?e.user.name.split(" ")[0]:e.user.email,s="premium"===e.user.subscriptionTier;return(0,n.jsxs)("div",{className:u().container,children:[(0,n.jsxs)("span",{className:u().userInfo,title:e.user.email,children:["Hi, ",t,s&&(0,n.jsx)("span",{className:u().premiumBadge,children:"Premium"})]}),(0,n.jsx)("button",{onClick:()=>(0,l.signOut)(),className:u().button,children:"Sign out"})]})}return(0,n.jsx)("div",{className:u().container,children:(0,n.jsx)("button",{onClick:()=>(0,l.signIn)("google",{callbackUrl:"/"}),className:"".concat(u().button," ").concat(u().signInButton),children:"Sign in with Google"})})}var h=()=>(0,n.jsx)("nav",{className:a().navbar,children:(0,n.jsxs)("div",{className:a().container,children:[(0,n.jsx)(o(),{href:"/",legacyBehavior:!0,children:(0,n.jsx)("a",{className:a().title,children:"GhostLayer"})}),(0,n.jsxs)("div",{className:a().navLinks,children:[(0,n.jsx)(o(),{href:"/",legacyBehavior:!0,children:(0,n.jsx)("a",{className:a().navLink,children:"Home"})}),(0,n.jsx)(o(),{href:"/features",legacyBehavior:!0,children:(0,n.jsx)("a",{className:a().navLink,children:"Features"})}),(0,n.jsx)(o(),{href:"/pricing",legacyBehavior:!0,children:(0,n.jsx)("a",{className:a().navLink,children:"Pricing"})}),(0,n.jsx)(o(),{href:"/about",legacyBehavior:!0,children:(0,n.jsx)("a",{className:a().navLink,children:"About"})})]}),(0,n.jsx)(d,{})]})}),p=s(4055),m=s.n(p),x=()=>{let e=new Date().getFullYear();return(0,n.jsx)("footer",{className:m().footer,children:(0,n.jsx)("div",{className:m().container,children:(0,n.jsxs)("p",{children:["\xa9 ",e," GhostLayer. All rights reserved."]})})})},_=e=>{let{children:t}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h,{}),(0,n.jsx)("main",{style:{padding:"0 20px",minHeight:"70vh"},children:t}),(0,n.jsx)(x,{})]})}},4164:function(e,t,s){"use strict";var n=s(5893);s(7294);var r=s(1664),o=s.n(r),i=s(2193),a=s.n(i);t.Z=e=>{let{currentPage:t="home"}=e,s={home:[{href:"/features",title:"Explore GhostLayer Features",description:"Discover advanced AI text humanization capabilities and bypass techniques",keywords:"AI text humanizer features, bypass AI detection tools"},{href:"/about",title:"About GhostLayer Technology",description:"Learn how our AI humanization algorithms work to make text undetectable",keywords:"AI humanization technology, how GhostLayer works"},{href:"/pricing",title:"GhostLayer Pricing Plans",description:"Choose the perfect plan for your AI text humanization needs",keywords:"AI text humanizer pricing, GhostLayer plans"}],features:[{href:"/",title:"Try GhostLayer Free",description:"Start humanizing your AI text with our free online tool",keywords:"free AI text humanizer, try GhostLayer"},{href:"/about",title:"How GhostLayer Works",description:"Understanding the science behind AI detection bypass",keywords:"AI detection bypass technology"},{href:"/pricing",title:"Upgrade for More Features",description:"Unlock advanced humanization modes and higher word limits",keywords:"premium AI text humanizer"}],about:[{href:"/",title:"Start Using GhostLayer",description:"Transform your AI text into undetectable human-like content",keywords:"AI text humanization tool"},{href:"/features",title:"GhostLayer Features",description:"Explore all the ways GhostLayer can help bypass AI detection",keywords:"AI detection bypass features"},{href:"/pricing",title:"Choose Your Plan",description:"Find the right GhostLayer subscription for your needs",keywords:"AI humanizer subscription plans"}],pricing:[{href:"/",title:"Try Before You Buy",description:"Test GhostLayer's AI humanization with our free tier",keywords:"free AI text humanizer trial"},{href:"/features",title:"See All Features",description:"Compare features across different GhostLayer plans",keywords:"AI humanizer feature comparison"},{href:"/about",title:"Why Choose GhostLayer",description:"Learn why GhostLayer is the most effective AI text humanizer",keywords:"best AI text humanizer"}]},r=s[t]||s.home;return(0,n.jsxs)("div",{className:a().internalLinks,children:[(0,n.jsxs)("section",{className:a().mainLinks,children:[(0,n.jsx)("h3",{className:a().sectionTitle,children:"Explore More"}),(0,n.jsx)("div",{className:a().linkGrid,children:r.map((e,t)=>(0,n.jsx)(o(),{href:e.href,className:a().mainLink,children:(0,n.jsxs)("div",{className:a().linkContent,children:[(0,n.jsx)("h4",{className:a().linkTitle,children:e.title}),(0,n.jsx)("p",{className:a().linkDescription,children:e.description}),(0,n.jsx)("div",{className:a().linkKeywords,children:e.keywords}),(0,n.jsx)("span",{className:a().linkArrow,children:"→"})]})},t))})]}),(0,n.jsxs)("section",{className:a().relatedTopics,children:[(0,n.jsx)("h3",{className:a().sectionTitle,children:"Related Topics"}),(0,n.jsx)("div",{className:a().topicsGrid,children:[{title:"AI Detection Tools",links:[{text:"GPTZero Bypass",anchor:"#gptzero-bypass"},{text:"Turnitin AI Detection",anchor:"#turnitin-bypass"},{text:"Originality.ai Bypass",anchor:"#originality-bypass"},{text:"Winston AI Detection",anchor:"#winston-bypass"}]},{title:"Humanization Techniques",links:[{text:"Conservative Mode",anchor:"#conservative-humanization"},{text:"Balanced Processing",anchor:"#balanced-humanization"},{text:"Aggressive Bypass",anchor:"#aggressive-humanization"},{text:"Style Preservation",anchor:"#style-preservation"}]},{title:"Use Cases",links:[{text:"Academic Writing",anchor:"#academic-use"},{text:"Content Creation",anchor:"#content-creation"},{text:"Business Communication",anchor:"#business-writing"},{text:"Creative Writing",anchor:"#creative-writing"}]}].map((e,t)=>(0,n.jsxs)("div",{className:a().topicGroup,children:[(0,n.jsx)("h4",{className:a().topicTitle,children:e.title}),(0,n.jsx)("ul",{className:a().topicLinks,children:e.links.map((e,t)=>(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:e.anchor,className:a().topicLink,onClick:t=>{t.preventDefault();let s=document.querySelector(e.anchor);s&&s.scrollIntoView({behavior:"smooth"})},children:e.text})},t))})]},t))})]}),(0,n.jsx)("section",{className:a().breadcrumbs,children:(0,n.jsxs)("nav",{className:a().breadcrumbNav,children:[(0,n.jsx)(o(),{href:"/",className:a().breadcrumbLink,children:"Home"}),(0,n.jsx)("span",{className:a().breadcrumbSeparator,children:"/"}),"home"!==t&&(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("span",{className:a().breadcrumbCurrent,children:t.charAt(0).toUpperCase()+t.slice(1)})})]})}),(0,n.jsx)("section",{className:a().footerLinks,children:(0,n.jsxs)("div",{className:a().footerGrid,children:[(0,n.jsxs)("div",{className:a().footerColumn,children:[(0,n.jsx)("h5",{className:a().footerTitle,children:"AI Text Humanization"}),(0,n.jsxs)("ul",{className:a().footerList,children:[(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#how-to-humanize",className:a().footerLink,children:"How to Humanize AI Text"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#bypass-detection",className:a().footerLink,children:"Bypass AI Detection"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#undetectable-ai",className:a().footerLink,children:"Make AI Text Undetectable"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#ai-humanizer-free",className:a().footerLink,children:"Free AI Humanizer"})})]})]}),(0,n.jsxs)("div",{className:a().footerColumn,children:[(0,n.jsx)("h5",{className:a().footerTitle,children:"AI Detection Tools"}),(0,n.jsxs)("ul",{className:a().footerList,children:[(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#gptzero-bypass",className:a().footerLink,children:"GPTZero Bypass"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#turnitin-ai",className:a().footerLink,children:"Turnitin AI Detection"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#originality-ai",className:a().footerLink,children:"Originality.ai Bypass"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#winston-ai",className:a().footerLink,children:"Winston AI Bypass"})})]})]}),(0,n.jsxs)("div",{className:a().footerColumn,children:[(0,n.jsx)("h5",{className:a().footerTitle,children:"Resources"}),(0,n.jsxs)("ul",{className:a().footerList,children:[(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/features",className:a().footerLink,children:"Features"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/about",className:a().footerLink,children:"About"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/pricing",className:a().footerLink,children:"Pricing"})}),(0,n.jsx)("li",{children:(0,n.jsx)(o(),{href:"/#faq",className:a().footerLink,children:"FAQ"})})]})]})]})})]})}},2458:function(e,t,s){"use strict";var n=s(5893),r=s(9008),o=s.n(r);t.Z=e=>{let{title:t="GhostLayer - AI Text Humanizer | Bypass AI Detection Instantly",description:s="Transform AI-generated text into human-like content that bypasses AI detection. Free online tool with 95% success rate. Make ChatGPT, GPT-4, and Claude text undetectable instantly.",keywords:r="AI text humanizer, bypass AI detection, make AI text undetectable, humanize ChatGPT text, AI content detector bypass, paraphrase AI text, undetectable AI writing, AI detection remover",canonicalUrl:i,ogImage:a="/images/og-ghostlayer.png",ogType:l="website",twitterCard:c="summary_large_image",structuredData:u,noIndex:d=!1,alternateUrls:h=[]}=e,p="http://localhost:3003",m=i?"".concat(p).concat(i):p,x=a.startsWith("http")?a:"".concat(p).concat(a);return(0,n.jsxs)(o(),{children:[(0,n.jsx)("title",{children:t}),(0,n.jsx)("meta",{name:"title",content:t}),(0,n.jsx)("meta",{name:"description",content:s}),(0,n.jsx)("meta",{name:"keywords",content:r}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("meta",{name:"robots",content:d?"noindex, nofollow":"index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"}),(0,n.jsx)("meta",{name:"googlebot",content:"index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"}),(0,n.jsx)("meta",{name:"bingbot",content:"index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"}),(0,n.jsx)("link",{rel:"canonical",href:m}),h.map((e,t)=>(0,n.jsx)("link",{rel:"alternate",hrefLang:e.hrefLang,href:"".concat(p).concat(e.href)},t)),(0,n.jsx)("meta",{property:"og:type",content:l}),(0,n.jsx)("meta",{property:"og:url",content:m}),(0,n.jsx)("meta",{property:"og:title",content:t}),(0,n.jsx)("meta",{property:"og:description",content:s}),(0,n.jsx)("meta",{property:"og:image",content:x}),(0,n.jsx)("meta",{property:"og:image:width",content:"1200"}),(0,n.jsx)("meta",{property:"og:image:height",content:"630"}),(0,n.jsx)("meta",{property:"og:image:alt",content:"GhostLayer AI Text Humanizer - Make AI Text Undetectable"}),(0,n.jsx)("meta",{property:"og:site_name",content:"GhostLayer"}),(0,n.jsx)("meta",{property:"og:locale",content:"en_US"}),(0,n.jsx)("meta",{property:"twitter:card",content:c}),(0,n.jsx)("meta",{property:"twitter:url",content:m}),(0,n.jsx)("meta",{property:"twitter:title",content:t}),(0,n.jsx)("meta",{property:"twitter:description",content:s}),(0,n.jsx)("meta",{property:"twitter:image",content:x}),(0,n.jsx)("meta",{property:"twitter:image:alt",content:"GhostLayer AI Text Humanizer - Make AI Text Undetectable"}),(0,n.jsx)("meta",{property:"twitter:creator",content:"@GhostLayerAI"}),(0,n.jsx)("meta",{property:"twitter:site",content:"@GhostLayerAI"}),(0,n.jsx)("meta",{name:"author",content:"GhostLayer"}),(0,n.jsx)("meta",{name:"publisher",content:"GhostLayer"}),(0,n.jsx)("meta",{name:"application-name",content:"GhostLayer"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"GhostLayer"}),(0,n.jsx)("meta",{name:"theme-color",content:"#6366f1"}),(0,n.jsx)("meta",{name:"msapplication-TileColor",content:"#6366f1"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,n.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),u&&(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(u)}}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://www.google-analytics.com"}),(0,n.jsx)("link",{rel:"dns-prefetch",href:"//fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"dns-prefetch",href:"//fonts.gstatic.com"}),(0,n.jsx)("link",{rel:"dns-prefetch",href:"//www.google-analytics.com"})]})}},1005:function(e,t,s){"use strict";s.d(t,{P:function(){return n}});let n=async e=>{let t=await fetch("/api/process",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({message:"An unknown error occurred"}))).message||"Error: ".concat(t.status));return t.json()}},6853:function(e,t,s){"use strict";s.d(t,{Kx:function(){return l},R7:function(){return c},_t:function(){return r},hB:function(){return o},yi:function(){return i},zP:function(){return a}});let n=()=>({"@context":"https://schema.org","@type":"WebSite",name:"GhostLayer",alternateName:"GhostLayer AI Text Humanizer",url:"http://localhost:3003",description:"AI text humanization tool that makes AI-generated content undetectable by AI detection systems",potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:"".concat("http://localhost:3003","/?q={search_term_string}")},"query-input":"required name=search_term_string"}}),r=()=>({"@context":"https://schema.org","@type":"SoftwareApplication",name:"GhostLayer AI Text Humanizer",description:"Advanced AI text humanization tool that transforms AI-generated content into human-like text, bypassing AI detection systems with 95% success rate",url:"http://localhost:3003",applicationCategory:"ProductivityApplication",operatingSystem:"Web Browser",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"2847",bestRating:"5",worstRating:"1"},author:{"@type":"Organization",name:"GhostLayer",url:"http://localhost:3003"},datePublished:"2024-01-01",dateModified:new Date().toISOString().split("T")[0],keywords:"AI text humanizer, bypass AI detection, make AI text undetectable, humanize ChatGPT text, AI content detector bypass",featureList:["AI Detection Bypass","Text Humanization","Style Preservation","Bulk Processing","Real-time Processing","Multiple AI Model Support"]}),o=()=>({"@context":"https://schema.org","@type":"Organization",name:"GhostLayer",url:"http://localhost:3003",logo:"".concat("http://localhost:3003","/images/logo.png"),description:"Leading provider of AI text humanization technology, helping users create undetectable AI content",foundingDate:"2024",sameAs:["https://twitter.com/GhostLayerAI","https://linkedin.com/company/ghostlayer","https://github.com/ghostlayer"]}),i=e=>({"@context":"https://schema.org","@type":"FAQPage",mainEntity:e.map(e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}}))}),a=()=>({"@context":"https://schema.org","@type":"HowTo",name:"How to Make AI Text Undetectable",description:"Step-by-step guide to humanize AI-generated text and bypass AI detection systems",image:"".concat("http://localhost:3003","/images/how-to-humanize-ai-text.png"),totalTime:"PT2M",estimatedCost:{"@type":"MonetaryAmount",currency:"USD",value:"0"},supply:[{"@type":"HowToSupply",name:"AI-generated text"}],tool:[{"@type":"HowToTool",name:"GhostLayer AI Text Humanizer"}],step:[{"@type":"HowToStep",name:"Paste AI Text",text:"Copy and paste your AI-generated text into the input field",image:"".concat("http://localhost:3003","/images/step-1-paste-text.png")},{"@type":"HowToStep",name:"Select Humanization Level",text:"Choose your desired humanization strength from Conservative to Aggressive",image:"".concat("http://localhost:3003","/images/step-2-select-level.png")},{"@type":"HowToStep",name:"Click Humanize",text:"Press the Humanize button to transform your text",image:"".concat("http://localhost:3003","/images/step-3-humanize.png")},{"@type":"HowToStep",name:"Copy Results",text:"Copy your humanized, undetectable text and use it anywhere",image:"".concat("http://localhost:3003","/images/step-4-copy-results.png")}]}),l=(e,t,s,n)=>({"@context":"https://schema.org","@type":"Article",headline:e,description:t,author:{"@type":"Organization",name:"GhostLayer"},publisher:{"@type":"Organization",name:"GhostLayer",logo:{"@type":"ImageObject",url:"".concat("http://localhost:3003","/images/logo.png")}},datePublished:s,dateModified:n||s,mainEntityOfPage:{"@type":"WebPage","@id":"http://localhost:3003"}}),c=()=>[n(),r(),o(),a()]},500:function(e){e.exports={container:"AuthButtons_container__4PqZA",userInfo:"AuthButtons_userInfo__Vmw_l",button:"AuthButtons_button__Q63K9",signInButton:"AuthButtons_signInButton__5ImaM",loading:"AuthButtons_loading__paEJE",premiumBadge:"AuthButtons_premiumBadge___CThw"}},6268:function(e){e.exports={buttonContainer:"ProcessingButton_buttonContainer__c6Ssn",processButton:"ProcessingButton_processButton__Vkjb1",disabled:"ProcessingButton_disabled__3yNjh",loading:"ProcessingButton_loading__b5TJe",buttonContent:"ProcessingButton_buttonContent__rMk2_",buttonIcon:"ProcessingButton_buttonIcon__qKv7D",buttonText:"ProcessingButton_buttonText__T_Ym1",buttonBackground:"ProcessingButton_buttonBackground__eucuk",hovered:"ProcessingButton_hovered__4lNqO",rippleContainer:"ProcessingButton_rippleContainer__INPeD",ripple:"ProcessingButton_ripple__K9cKL",loadingSpinner:"ProcessingButton_loadingSpinner__4lmNI",spin:"ProcessingButton_spin__siVcu",statusContainer:"ProcessingButton_statusContainer__IrKG3",statusItem:"ProcessingButton_statusItem___5lAS",statusIcon:"ProcessingButton_statusIcon__WUDke",statusText:"ProcessingButton_statusText__qx88X",statusDot:"ProcessingButton_statusDot__2IEwg",pulse:"ProcessingButton_pulse__MI6uT",stepsContainer:"ProcessingButton_stepsContainer__Iu4pl",step:"ProcessingButton_step__4eG0b",stepDot:"ProcessingButton_stepDot__nGbbp",active:"ProcessingButton_active__SDUUP",stepPulse:"ProcessingButton_stepPulse__MXMqi",stepText:"ProcessingButton_stepText__OZIZj"}},7205:function(e){e.exports={resultsDisplay:"ResultsDisplay_resultsDisplay__oyPvC",header:"ResultsDisplay_header__RpTVe",icon:"ResultsDisplay_icon__IIGHr",content:"ResultsDisplay_content__lRvw9",loadingContainer:"ResultsDisplay_loadingContainer__u8opG",loadingSpinner:"ResultsDisplay_loadingSpinner__z6zR5",spin:"ResultsDisplay_spin__6XbWN",statusSection:"ResultsDisplay_statusSection__3kYFh",statusItem:"ResultsDisplay_statusItem__L_Ws4",statusBadge:"ResultsDisplay_statusBadge__3kx_B",statusHuman:"ResultsDisplay_statusHuman__wl0RF",statusAI:"ResultsDisplay_statusAI__W_9aU",statusMixed:"ResultsDisplay_statusMixed__PvK6D",scoreSection:"ResultsDisplay_scoreSection__rLZcX",scoreItem:"ResultsDisplay_scoreItem__0wf4p",scoreDisplay:"ResultsDisplay_scoreDisplay__5IAMN",scoreValue:"ResultsDisplay_scoreValue__mkNHJ",scoreBar:"ResultsDisplay_scoreBar__sDHpM",scoreProgress:"ResultsDisplay_scoreProgress__J9QMm",messageSection:"ResultsDisplay_messageSection__ssZjs",message:"ResultsDisplay_message__o59B5",summary:"ResultsDisplay_summary__kLGYP",summaryText:"ResultsDisplay_summaryText__2NBMU",errorMessage:"ResultsDisplay_errorMessage__bZd6K"}},3709:function(e){e.exports={editorContainer:"TextEditor_editorContainer__fD3wr",editorSection:"TextEditor_editorSection__QRZXp",editorHeader:"TextEditor_editorHeader__VSiH9",headerLeft:"TextEditor_headerLeft__n6r_W",editorTitle:"TextEditor_editorTitle__CCcLD",titleIcon:"TextEditor_titleIcon__V_DvV",stats:"TextEditor_stats__2hJj3",stat:"TextEditor_stat__sXU4h",headerActions:"TextEditor_headerActions__46sB3",actionButton:"TextEditor_actionButton__USUq1",editorWrapper:"TextEditor_editorWrapper__g5s6E",focused:"TextEditor_focused__3OZ_L",textarea:"TextEditor_textarea__FOlYI",textareaOutput:"TextEditor_textareaOutput__Rxt8T",loadingOverlay:"TextEditor_loadingOverlay__U4U_b",loadingSpinner:"TextEditor_loadingSpinner__RG7nE",spin:"TextEditor_spin__o5hyz",separator:"TextEditor_separator__dknt_",arrowContainer:"TextEditor_arrowContainer__fXf3V",pulse:"TextEditor_pulse__NULAC",arrow:"TextEditor_arrow__ks1DT",emptyState:"TextEditor_emptyState__L6j5_",emptyIcon:"TextEditor_emptyIcon__WTC_3"}},4055:function(e){e.exports={footer:"Footer_footer__eNA9m",container:"Footer_container__IkLXC",footerLinks:"Footer_footerLinks__BIHhl"}},9559:function(e){e.exports={navbar:"Navbar_navbar__3BIeH",container:"Navbar_container__vazkH",title:"Navbar_title__fJOSl",navLinks:"Navbar_navLinks__chCYm",navLink:"Navbar_navLink__A03oH"}},5202:function(e){e.exports={app:"Home_app__8F103",main:"Home_main__2uIek",hero:"Home_hero__g_og0",heroContent:"Home_heroContent__IGkft",heroTitle:"Home_heroTitle__BwshW",highlight:"Home_highlight__TZ0SE",heroSubtitle:"Home_heroSubtitle__C6BcQ",signInPrompt:"Home_signInPrompt__ZzMJh",statusBox:"Home_statusBox__lX_cs",premiumStatus:"Home_premiumStatus__5qOQy",freeStatus:"Home_freeStatus__wPgPg",upgradePrompt:"Home_upgradePrompt__wjeY1",heroStats:"Home_heroStats__hwR4e",stat:"Home_stat__N0Ae2",statNumber:"Home_statNumber__IqK0v",statLabel:"Home_statLabel__rkjtq",processingSection:"Home_processingSection__VOLHE",errorMessage:"Home_errorMessage__X_0TQ",statsDisplay:"Home_statsDisplay__wozbP",statsGrid:"Home_statsGrid___Y9es",statItem:"Home_statItem__EtTZS",statValue:"Home_statValue__AfjME"}},2193:function(e){e.exports={internalLinks:"InternalLinks_internalLinks__lYQNE",sectionTitle:"InternalLinks_sectionTitle__q5IHW",mainLinks:"InternalLinks_mainLinks__cD1jA",linkGrid:"InternalLinks_linkGrid__6ufhf",mainLink:"InternalLinks_mainLink__eAtC_",linkContent:"InternalLinks_linkContent__Li5pb",linkTitle:"InternalLinks_linkTitle__HncYw",linkDescription:"InternalLinks_linkDescription__atono",linkKeywords:"InternalLinks_linkKeywords__puE7f",linkArrow:"InternalLinks_linkArrow__j_r_W",relatedTopics:"InternalLinks_relatedTopics__Uf6mk",topicsGrid:"InternalLinks_topicsGrid__xlFXO",topicGroup:"InternalLinks_topicGroup__ZQ4x6",topicTitle:"InternalLinks_topicTitle__nOT6F",topicLinks:"InternalLinks_topicLinks__iYNE_",topicLink:"InternalLinks_topicLink__gmV1Z",breadcrumbs:"InternalLinks_breadcrumbs__eWOgy",breadcrumbNav:"InternalLinks_breadcrumbNav__0HvyR",breadcrumbLink:"InternalLinks_breadcrumbLink__5C3Yy",breadcrumbSeparator:"InternalLinks_breadcrumbSeparator__GcCWj",breadcrumbCurrent:"InternalLinks_breadcrumbCurrent__AEwX5",footerLinks:"InternalLinks_footerLinks__2pniY",footerGrid:"InternalLinks_footerGrid__2Stif",footerColumn:"InternalLinks_footerColumn__uaAyx",footerTitle:"InternalLinks_footerTitle__ub74I",footerList:"InternalLinks_footerList__NspRt",footerLink:"InternalLinks_footerLink__qzjjk"}},614:function(e){e.exports={pricingContainer:"PricingPage_pricingContainer__7Yj6a",pageTitle:"PricingPage_pageTitle__ANwQB",pageSubtitle:"PricingPage_pageSubtitle__WNm22",plansGrid:"PricingPage_plansGrid__Z21SC",planCard:"PricingPage_planCard__xw58e",currentPlan:"PricingPage_currentPlan__MRw4i",planTitle:"PricingPage_planTitle__RyU8o",planPrice:"PricingPage_planPrice__2mMDD",pricePer:"PricingPage_pricePer__7R3XP",featuresList:"PricingPage_featuresList__wfhPL",planButton:"PricingPage_planButton___Uxvy",upgradeButton:"PricingPage_upgradeButton__0g5s9",loadingMessage:"PricingPage_loadingMessage__KhEhg",errorMessage:"PricingPage_errorMessage__BNvZw",warningMessage:"PricingPage_warningMessage__ipQtN"}},9008:function(e,t,s){e.exports=s(3867)},1664:function(e,t,s){e.exports=s(8342)},1163:function(e,t,s){e.exports=s(3079)}}]);