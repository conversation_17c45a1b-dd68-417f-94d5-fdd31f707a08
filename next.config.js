/** @type {import('next').NextConfig} */
const nextConfig = {
  // Conditionally enable static export only for production builds
  // This allows API routes to work during development
  ...(process.env.NODE_ENV === 'production' && process.env.NETLIFY_BUILD === 'true' ? {
    output: 'export',
  } : {}),

  // Disable image optimization for static export
  images: {
    unoptimized: true,
  },

  // Configure trailing slash for consistent URLs
  trailingSlash: true,

  // Disable server-side features for static export
  experimental: {
    // Remove deprecated appDir option
  },

  // Configure redirects (will be handled by Netlify _redirects file)
  async redirects() {
    return [
      // Example redirects - these will be converted to Netlify format
      {
        source: '/old-page',
        destination: '/',
        permanent: true,
      },
    ];
  },

  // Configure headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },

  // Configure compression
  compress: true,

  // Configure power-by header
  poweredByHeader: false,

  // Configure React strict mode
  reactStrictMode: false,

  // Configure SWC minification
  swcMinify: false,

  // Configure ESLint
  eslint: {
    // Only run ESLint on these directories during production builds
    dirs: ['pages', 'src'],
    // Ignore ESLint errors during builds for faster deployment
    ignoreDuringBuilds: true,
  },

  // Configure TypeScript
  typescript: {
    // Ignore TypeScript errors during builds for faster deployment
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;
