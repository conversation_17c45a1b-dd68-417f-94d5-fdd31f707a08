/** @type {import('next').NextConfig} */
const nextConfig = {
  // Optimize for Netlify deployment with conditional static export
  // Static export for Netlify, dynamic for development
  ...(process.env.NETLIFY === 'true' ? {
    output: 'export',
    distDir: 'out',
  } : {}),

  // Disable image optimization for static export (Netlify requirement)
  images: {
    unoptimized: process.env.NETLIFY === 'true',
    // Configure domains for external images if needed
    domains: [],
    // Configure image formats for non-static builds
    formats: ['image/webp', 'image/avif'],
  },

  // Configure trailing slash for consistent URLs
  trailingSlash: true, // Netlify works better with trailing slashes

  // Enable experimental features for better performance
  experimental: {
    // Enable modern bundling for smaller builds
    esmExternals: true,
    // Enable server components optimization
    serverComponentsExternalPackages: ['prisma', '@prisma/client'],
  },

  // Configure redirects
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/old-page',
        destination: '/',
        permanent: true,
      },
    ];
  },

  // Configure headers for security and performance (disabled for static export)
  async headers() {
    // Skip headers for static export (Netlify handles this via _headers file)
    if (process.env.NETLIFY === 'true') {
      return [];
    }

    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
    ];
  },

  // Configure compression
  compress: true,

  // Configure power-by header
  poweredByHeader: false,

  // Enable React strict mode for better development experience
  reactStrictMode: true,

  // Enable SWC minification for better performance
  swcMinify: true,

  // Configure ESLint
  eslint: {
    // Only run ESLint on these directories during production builds
    dirs: ['pages', 'src'],
    // Ignore ESLint errors during builds for faster deployment
    ignoreDuringBuilds: true,
  },

  // Configure TypeScript
  typescript: {
    // Ignore TypeScript errors during builds for faster deployment
    ignoreBuildErrors: true,
  },

  // Configure webpack for better bundling
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Optimize bundle size
    if (!dev && !isServer) {
      config.optimization.splitChunks.cacheGroups = {
        ...config.optimization.splitChunks.cacheGroups,
        commons: {
          name: 'commons',
          chunks: 'all',
          minChunks: 2,
          enforce: true,
        },
      };
    }

    return config;
  },
};

module.exports = nextConfig;
