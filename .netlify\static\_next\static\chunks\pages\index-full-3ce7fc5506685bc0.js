(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{626:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/index-full",function(){return t(5140)}])},5140:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return p}});var n=t(5893),a=t(7294),i=t(9008),r=t.n(i),l=t(3299),o=t(6993),c=t(2377),d=t(1616),h=t(5337),m=t(1005),x=t(5202),u=t.n(x);function p(){let{data:e,status:s}=(0,l.useSession)(),[t,i]=(0,a.useState)(""),[x,p]=(0,a.useState)(""),[j,g]=(0,a.useState)(null),[N,f]=(0,a.useState)(!1),[b,y]=(0,a.useState)(""),[v,w]=(0,a.useState)(null),T=async()=>{if(!t.trim()){y("Please enter some text to process.");return}f(!0),y(""),p(""),g(null),w(null);let e=Date.now();try{let s=await (0,m.P)({text:t}),n=Date.now()-e;p(s.modifiedText),g(s.detectionResult),w({processingTime:n,originalLength:t.length,modifiedLength:s.modifiedText.length,wordsChanged:L(t,s.modifiedText)})}catch(e){y(e.message||"Failed to process text. Please try again."),console.error("Processing error:",e)}finally{f(!1)}},L=(e,s)=>{let t=e.trim().split(/\s+/),n=s.trim().split(/\s+/),a=0;for(let e=0;e<Math.min(t.length,n.length);e++)t[e]!==n[e]&&a++;return a+Math.abs(t.length-n.length)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(r(),{children:[(0,n.jsx)("title",{children:"GhostLayer - Transform AI Text to Human-Like Content"}),(0,n.jsx)("meta",{name:"description",content:"Transform AI-generated text into human-like content that bypasses AI detection. Free online tool with advanced paraphrasing and text modification."}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("meta",{property:"og:title",content:"GhostLayer - Transform AI Text to Human-Like Content"}),(0,n.jsx)("meta",{property:"og:description",content:"Transform AI-generated text into human-like content that bypasses AI detection."}),(0,n.jsx)("meta",{property:"og:type",content:"website"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,n.jsx)(o.Z,{children:(0,n.jsx)("div",{className:u().app,children:(0,n.jsxs)("main",{className:u().main,children:[(0,n.jsx)("section",{className:u().hero,children:(0,n.jsxs)("div",{className:u().heroContent,children:[(0,n.jsxs)("h1",{className:u().heroTitle,children:["Transform AI Text to",(0,n.jsx)("span",{className:u().highlight,children:" Human-Like Content"})]}),(0,n.jsx)("p",{className:u().heroSubtitle,children:"Make your AI-generated text undetectable with our advanced paraphrasing and text modification technology. Free to use, instant results."}),(0,n.jsxs)("div",{className:u().heroStats,children:[(0,n.jsxs)("div",{className:u().stat,children:[(0,n.jsx)("span",{className:u().statNumber,children:"10K+"}),(0,n.jsx)("span",{className:u().statLabel,children:"Texts Processed"})]}),(0,n.jsxs)("div",{className:u().stat,children:[(0,n.jsx)("span",{className:u().statNumber,children:"95%"}),(0,n.jsx)("span",{className:u().statLabel,children:"Success Rate"})]}),(0,n.jsxs)("div",{className:u().stat,children:[(0,n.jsx)("span",{className:u().statNumber,children:"< 5s"}),(0,n.jsx)("span",{className:u().statLabel,children:"Processing Time"})]})]})]})}),(0,n.jsxs)("section",{className:u().processingSection,children:[(0,n.jsx)(c.Z,{inputText:t,onInputChange:e=>{i(e.target.value),b&&y("")},outputText:x,isLoading:N,onCopy:(e,s)=>{console.log("Copied ".concat(s," text:"),e.substring(0,50)+"...")},onDownload:e=>{let s=new Blob([e],{type:"text/plain"}),t=URL.createObjectURL(s),n=document.createElement("a");n.href=t,n.download="humanized-text.txt",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(t)}}),(0,n.jsx)(d.Z,{onProcess:T,isLoading:N,disabled:!t.trim(),inputText:t}),b&&(0,n.jsxs)("div",{className:u().errorMessage,children:[(0,n.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor",strokeWidth:"2"})]}),(0,n.jsx)("span",{children:b})]}),v&&(0,n.jsxs)("div",{className:u().statsDisplay,children:[(0,n.jsx)("h3",{children:"Processing Results"}),(0,n.jsxs)("div",{className:u().statsGrid,children:[(0,n.jsxs)("div",{className:u().statItem,children:[(0,n.jsxs)("span",{className:u().statValue,children:[v.processingTime,"ms"]}),(0,n.jsx)("span",{className:u().statLabel,children:"Processing Time"})]}),(0,n.jsxs)("div",{className:u().statItem,children:[(0,n.jsx)("span",{className:u().statValue,children:v.wordsChanged}),(0,n.jsx)("span",{className:u().statLabel,children:"Words Modified"})]}),(0,n.jsxs)("div",{className:u().statItem,children:[(0,n.jsxs)("span",{className:u().statValue,children:[Math.round(v.wordsChanged/t.trim().split(/\s+/).length*100),"%"]}),(0,n.jsx)("span",{className:u().statLabel,children:"Change Rate"})]})]})]}),j&&(0,n.jsx)(h.Z,{outputText:x,detectionResult:j,isLoading:N})]})]})})})]})}}},function(e){e.O(0,[351,888,774,179],function(){return e(e.s=626)}),_N_E=e.O()}]);