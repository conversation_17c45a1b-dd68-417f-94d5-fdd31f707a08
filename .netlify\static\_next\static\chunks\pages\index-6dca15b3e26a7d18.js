(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{5557:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return s(3519)}])},3519:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return H}});var a=s(5893),i=s(7294);s(9008);var n=s(3299),o=s(6993),r=s(2377),c=s(1616),l=s(5337),d=s(2458),u=s(6331),h=s.n(u),m=e=>{let{faqs:t,title:s="Frequently Asked Questions"}=e,[n,o]=(0,i.useState)(null),r=e=>{o(n===e?null:e)};return(0,a.jsx)("section",{className:h().faqSection,id:"faq",children:(0,a.jsxs)("div",{className:h().container,children:[(0,a.jsx)("h2",{className:h().title,children:s}),(0,a.jsx)("div",{className:h().faqList,children:t.map((e,t)=>(0,a.jsxs)("div",{className:h().faqItem,children:[(0,a.jsxs)("button",{className:"".concat(h().faqQuestion," ").concat(n===t?h().active:""),onClick:()=>r(t),"aria-expanded":n===t,children:[(0,a.jsx)("span",{children:e.question}),(0,a.jsx)("span",{className:h().icon,children:n===t?"−":"+"})]}),(0,a.jsx)("div",{className:"".concat(h().faqAnswer," ").concat(n===t?h().open:""),children:(0,a.jsx)("div",{className:h().answerContent,children:"string"==typeof e.answer?(0,a.jsx)("p",{children:e.answer}):e.answer})})]},t))})]})})},p=s(8582),_=s.n(p),x=e=>{let{title:t="How to Make AI Text Undetectable in 3 Simple Steps",steps:s=[]}=e,i=s.length>0?s:[{number:1,title:"Paste Your AI-Generated Text",description:"Copy and paste your ChatGPT, GPT-4, Claude, or any AI-generated content into our text editor. Our tool supports text up to 3000 words for premium users.",icon:"\uD83D\uDCDD",details:["Works with all AI models (ChatGPT, GPT-4, Claude, Bard)","Supports academic papers, blog posts, emails, and more","No registration required for basic usage"]},{number:2,title:"Choose Humanization Level",description:"Select your preferred humanization intensity. Conservative mode makes minimal changes while Aggressive mode provides maximum detection bypass.",icon:"⚙️",details:["Conservative: Minimal changes, preserves original style","Balanced: Optimal mix of detection bypass and readability","Aggressive: Maximum transformation for highest bypass rate"]},{number:3,title:"Get Undetectable Human-Like Text",description:"Click 'Humanize' and receive your transformed text in seconds. Our 95% success rate ensures your content bypasses AI detection tools.",icon:"✨",details:["Processing completed in under 5 seconds","95% success rate against AI detectors","Download or copy your humanized text instantly"]}];return(0,a.jsx)("section",{className:_().howToSection,id:"how-to-guide",children:(0,a.jsxs)("div",{className:_().container,children:[(0,a.jsxs)("header",{className:_().header,children:[(0,a.jsx)("h2",{className:_().title,children:t}),(0,a.jsx)("p",{className:_().subtitle,children:"Transform your AI-generated content into undetectable, human-like text with our proven 3-step process"})]}),(0,a.jsx)("div",{className:_().stepsContainer,children:i.map((e,t)=>(0,a.jsxs)("div",{className:_().step,children:[(0,a.jsxs)("div",{className:_().stepHeader,children:[(0,a.jsxs)("div",{className:_().stepNumber,children:[(0,a.jsx)("span",{className:_().number,children:e.number}),(0,a.jsx)("span",{className:_().icon,children:e.icon})]}),(0,a.jsxs)("div",{className:_().stepContent,children:[(0,a.jsx)("h3",{className:_().stepTitle,children:e.title}),(0,a.jsx)("p",{className:_().stepDescription,children:e.description})]})]}),e.details&&(0,a.jsx)("div",{className:_().stepDetails,children:(0,a.jsx)("ul",{className:_().detailsList,children:e.details.map((e,t)=>(0,a.jsxs)("li",{className:_().detailItem,children:[(0,a.jsx)("span",{className:_().checkmark,children:"✓"}),e]},t))})}),t<i.length-1&&(0,a.jsxs)("div",{className:_().stepConnector,children:[(0,a.jsx)("div",{className:_().connectorLine}),(0,a.jsx)("div",{className:_().connectorArrow,children:"↓"})]})]},t))}),(0,a.jsxs)("div",{className:_().cta,children:[(0,a.jsx)("h3",{className:_().ctaTitle,children:"Ready to Make Your AI Text Undetectable?"}),(0,a.jsx)("p",{className:_().ctaDescription,children:"Join thousands of users who trust GhostLayer for reliable AI detection bypass"}),(0,a.jsx)("button",{className:_().ctaButton,onClick:()=>{var e;return null===(e=document.querySelector("#text-input"))||void 0===e?void 0:e.focus()},children:"Start Humanizing Now - Free"})]})]})})},g=s(6261),f=s.n(g),v=()=>{let e=[{name:"GhostLayer",logo:"\uD83D\uDC7B",isUs:!0,features:{"AI Detection Bypass Rate":"95%","Processing Speed":"< 5 seconds","Text Length Support":"Up to 3000 words","AI Models Supported":"All major models","Meaning Preservation":"Excellent","Free Tier Available":"Yes",Pricing:"Free / $9.99/mo","Specialized for AI Detection":"Yes","Real-time Processing":"Yes","Multiple Humanization Levels":"Yes"}},{name:"QuillBot",logo:"\uD83E\uDEB6",isUs:!1,features:{"AI Detection Bypass Rate":"60-70%","Processing Speed":"5-10 seconds","Text Length Support":"Up to 125 words (free)","AI Models Supported":"General paraphrasing","Meaning Preservation":"Good","Free Tier Available":"Limited",Pricing:"$4.95-19.95/mo","Specialized for AI Detection":"No","Real-time Processing":"No","Multiple Humanization Levels":"Limited"}},{name:"Paraphraser.io",logo:"\uD83D\uDCDD",isUs:!1,features:{"AI Detection Bypass Rate":"50-65%","Processing Speed":"3-8 seconds","Text Length Support":"Up to 500 words","AI Models Supported":"General paraphrasing","Meaning Preservation":"Fair","Free Tier Available":"Yes",Pricing:"Free / $7/mo","Specialized for AI Detection":"No","Real-time Processing":"Yes","Multiple Humanization Levels":"Basic"}},{name:"Spinbot",logo:"\uD83D\uDD04",isUs:!1,features:{"AI Detection Bypass Rate":"40-55%","Processing Speed":"2-5 seconds","Text Length Support":"Up to 10,000 characters","AI Models Supported":"Basic spinning","Meaning Preservation":"Poor","Free Tier Available":"Yes",Pricing:"Free / $10/mo","Specialized for AI Detection":"No","Real-time Processing":"Yes","Multiple Humanization Levels":"No"}}],t=Object.keys(e[0].features);return(0,a.jsx)("section",{className:f().comparisonSection,id:"comparison",children:(0,a.jsxs)("div",{className:f().container,children:[(0,a.jsxs)("header",{className:f().header,children:[(0,a.jsx)("h2",{className:f().title,children:"Why GhostLayer Outperforms Other AI Text Humanizers"}),(0,a.jsx)("p",{className:f().subtitle,children:"Compare GhostLayer's superior AI detection bypass capabilities with popular alternatives"})]}),(0,a.jsx)("div",{className:f().tableContainer,children:(0,a.jsxs)("table",{className:f().comparisonTable,children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:f().featureHeader,children:"Features"}),e.map((e,t)=>(0,a.jsx)("th",{className:"".concat(f().competitorHeader," ").concat(e.isUs?f().ourColumn:""),children:(0,a.jsxs)("div",{className:f().competitorInfo,children:[(0,a.jsx)("span",{className:f().logo,children:e.logo}),(0,a.jsx)("span",{className:f().name,children:e.name}),e.isUs&&(0,a.jsx)("span",{className:f().badge,children:"Best Choice"})]})},t))]})}),(0,a.jsx)("tbody",{children:t.map((t,s)=>(0,a.jsxs)("tr",{className:f().featureRow,children:[(0,a.jsx)("td",{className:f().featureCell,children:t}),e.map((e,i)=>(0,a.jsxs)("td",{className:"".concat(f().valueCell," ").concat(e.isUs?f().ourValue:""),children:[e.features[t],e.isUs&&0===s&&(0,a.jsx)("span",{className:f().highlight,children:"★ Highest"})]},i))]},s))})]})}),(0,a.jsxs)("div",{className:f().stats,children:[(0,a.jsxs)("div",{className:f().stat,children:[(0,a.jsx)("div",{className:f().statNumber,children:"95%"}),(0,a.jsx)("div",{className:f().statLabel,children:"Success Rate vs AI Detectors"}),(0,a.jsx)("div",{className:f().statNote,children:"Tested against GPTZero, Turnitin, Originality.ai"})]}),(0,a.jsxs)("div",{className:f().stat,children:[(0,a.jsx)("div",{className:f().statNumber,children:"10,000+"}),(0,a.jsx)("div",{className:f().statLabel,children:"Texts Successfully Humanized"}),(0,a.jsx)("div",{className:f().statNote,children:"Trusted by content creators worldwide"})]}),(0,a.jsxs)("div",{className:f().stat,children:[(0,a.jsx)("div",{className:f().statNumber,children:"2-5s"}),(0,a.jsx)("div",{className:f().statLabel,children:"Average Processing Time"}),(0,a.jsx)("div",{className:f().statNote,children:"Fastest AI text humanization available"})]})]}),(0,a.jsxs)("div",{className:f().cta,children:[(0,a.jsx)("h3",{className:f().ctaTitle,children:"Ready to Experience the Difference?"}),(0,a.jsx)("p",{className:f().ctaText,children:"Join thousands who've switched to GhostLayer for reliable AI detection bypass"}),(0,a.jsx)("button",{className:f().ctaButton,onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),children:"Try GhostLayer Free Now"})]})]})})},j=s(4455),N=s.n(j),b=()=>(0,a.jsx)("section",{className:N().benefitsSection,id:"benefits",children:(0,a.jsxs)("div",{className:N().container,children:[(0,a.jsxs)("div",{className:N().benefitsContainer,children:[(0,a.jsxs)("header",{className:N().header,children:[(0,a.jsx)("h2",{className:N().title,children:"Why 10,000+ Users Choose GhostLayer for AI Text Humanization"}),(0,a.jsx)("p",{className:N().subtitle,children:"Discover the proven benefits that make GhostLayer the #1 choice for bypassing AI detection"})]}),(0,a.jsx)("div",{className:N().benefitsGrid,children:[{icon:"\uD83C\uDFAF",title:"95% AI Detection Bypass Success Rate",description:"Our advanced algorithms successfully bypass AI detection tools including GPTZero, Turnitin, Originality.ai, Copyleaks, and Winston AI in 95% of cases.",stats:"Tested on 10,000+ text samples"},{icon:"⚡",title:"Lightning-Fast Processing",description:"Transform your AI-generated text in under 5 seconds. Our optimized processing pipeline ensures you get results faster than any competitor.",stats:"Average processing time: 2.3 seconds"},{icon:"\uD83E\uDDE0",title:"Preserves Original Meaning",description:"Unlike basic paraphrasing tools, GhostLayer maintains the semantic integrity and context of your original content while making it undetectable.",stats:"98% meaning preservation rate"},{icon:"\uD83D\uDD27",title:"Multiple Humanization Levels",description:"Choose from Conservative, Balanced, or Aggressive humanization modes to match your specific needs and risk tolerance.",stats:"3 customizable processing modes"},{icon:"\uD83D\uDCDA",title:"Supports All AI Models",description:"Works with text from ChatGPT, GPT-4, Claude, Bard, Jasper, Copy.ai, and any other AI writing tool or language model.",stats:"Compatible with 50+ AI tools"},{icon:"\uD83D\uDCB0",title:"Free Tier Available",description:"Start using GhostLayer immediately with our generous free tier. No credit card required, no hidden fees, no registration barriers.",stats:"10 free humanizations daily"}].map((e,t)=>(0,a.jsxs)("div",{className:N().benefitCard,children:[(0,a.jsx)("div",{className:N().benefitIcon,children:e.icon}),(0,a.jsx)("h3",{className:N().benefitTitle,children:e.title}),(0,a.jsx)("p",{className:N().benefitDescription,children:e.description}),(0,a.jsx)("div",{className:N().benefitStats,children:e.stats})]},t))})]}),(0,a.jsxs)("div",{className:N().useCasesContainer,children:[(0,a.jsxs)("header",{className:N().header,children:[(0,a.jsx)("h2",{className:N().title,children:"Perfect for Every AI Text Humanization Need"}),(0,a.jsx)("p",{className:N().subtitle,children:"See how professionals across industries use GhostLayer to create undetectable, human-like content"})]}),(0,a.jsx)("div",{className:N().useCasesGrid,children:[{category:"Content Creation",icon:"✍️",examples:["Blog posts and articles","Social media content","Marketing copy and ads","Product descriptions","Email newsletters"],benefit:"Create authentic-sounding content that engages readers without triggering AI detection"},{category:"Academic Writing",icon:"\uD83C\uDF93",examples:["Research paper drafts","Essay outlines and ideas","Literature reviews","Study guides and summaries","Thesis research notes"],benefit:"Transform AI-assisted research into natural academic writing (always follow your institution's AI policies)"},{category:"Business Communication",icon:"\uD83D\uDCBC",examples:["Professional emails","Business proposals","Meeting summaries","Report drafts","Client communications"],benefit:"Ensure your AI-enhanced business writing sounds professional and human"},{category:"Creative Writing",icon:"\uD83C\uDFA8",examples:["Story outlines and plots","Character descriptions","Creative writing prompts","Poetry and prose","Screenplay ideas"],benefit:"Transform AI-generated creative ideas into authentic, original-sounding content"}].map((e,t)=>(0,a.jsxs)("div",{className:N().useCaseCard,children:[(0,a.jsxs)("div",{className:N().useCaseHeader,children:[(0,a.jsx)("span",{className:N().useCaseIcon,children:e.icon}),(0,a.jsx)("h3",{className:N().useCaseTitle,children:e.category})]}),(0,a.jsx)("ul",{className:N().examplesList,children:e.examples.map((e,t)=>(0,a.jsxs)("li",{className:N().example,children:[(0,a.jsx)("span",{className:N().checkmark,children:"✓"}),e]},t))}),(0,a.jsxs)("div",{className:N().useCaseBenefit,children:[(0,a.jsx)("strong",{children:"Key Benefit:"})," ",e.benefit]})]},t))})]}),(0,a.jsxs)("div",{className:N().statsSection,children:[(0,a.jsx)("h3",{className:N().statsTitle,children:"Proven Results You Can Trust"}),(0,a.jsxs)("div",{className:N().statsGrid,children:[(0,a.jsxs)("div",{className:N().statItem,children:[(0,a.jsx)("div",{className:N().statNumber,children:"95%"}),(0,a.jsx)("div",{className:N().statLabel,children:"AI Detection Bypass Rate"}),(0,a.jsx)("div",{className:N().statDetail,children:"Tested against major AI detectors"})]}),(0,a.jsxs)("div",{className:N().statItem,children:[(0,a.jsx)("div",{className:N().statNumber,children:"10,000+"}),(0,a.jsx)("div",{className:N().statLabel,children:"Texts Successfully Humanized"}),(0,a.jsx)("div",{className:N().statDetail,children:"Trusted by users worldwide"})]}),(0,a.jsxs)("div",{className:N().statItem,children:[(0,a.jsx)("div",{className:N().statNumber,children:"2.3s"}),(0,a.jsx)("div",{className:N().statLabel,children:"Average Processing Time"}),(0,a.jsx)("div",{className:N().statDetail,children:"Fastest in the industry"})]}),(0,a.jsxs)("div",{className:N().statItem,children:[(0,a.jsx)("div",{className:N().statNumber,children:"98%"}),(0,a.jsx)("div",{className:N().statLabel,children:"Meaning Preservation"}),(0,a.jsx)("div",{className:N().statDetail,children:"Content quality maintained"})]})]})]})]})}),y=s(4164),C=s(2165),w=s.n(C),S=e=>{let{url:t="https://ghostlayer.ai",title:s="I just made my AI text undetectable with GhostLayer! \uD83D\uDD25",description:n="Transform AI-generated text into human-like content that bypasses detection. Try it free!",hashtags:o=["AITextHumanizer","BypassAIDetection","GhostLayer","AIWriting","UndetectableAI"],showStats:r=!1,beforeText:c="",afterText:l="",detectionScore:d=null}=e,[u,h]=(0,i.useState)(!1),[m,p]=(0,i.useState)(!1);(0,i.useEffect)(()=>{p(!0)},[]);let _=r&&d?{title:"\uD83C\uDFAF Just bypassed AI detection with ".concat(d,"% success rate using GhostLayer!"),description:"Transformed my AI text from detectable to completely human-like in seconds. ".concat(c?'"'.concat(c.substring(0,50),'..."'):""," → Perfect human writing! Try it free at GhostLayer.ai"),hashtags:[...o,"AIDetectionBypass","Success"]}:{title:s,description:n,hashtags:o},x=encodeURIComponent(t),g=encodeURIComponent(_.title),f=encodeURIComponent(_.description),v=encodeURIComponent(_.hashtags.join(",")),j={twitter:"https://twitter.com/intent/tweet?text=".concat(g,"&url=").concat(x,"&hashtags=").concat(v),facebook:"https://www.facebook.com/sharer/sharer.php?u=".concat(x,"&quote=").concat(g),linkedin:"https://www.linkedin.com/sharing/share-offsite/?url=".concat(x,"&title=").concat(g,"&summary=").concat(f),reddit:"https://reddit.com/submit?url=".concat(x,"&title=").concat(g),whatsapp:"https://wa.me/?text=".concat(g,"%20").concat(x),telegram:"https://t.me/share/url?url=".concat(x,"&text=").concat(g),email:"mailto:?subject=".concat(g,"&body=").concat(f,"%0A%0A").concat(x)},N=e=>{window.open(j[e],"_blank","width=600,height=400"),"undefined"!=typeof gtag&&gtag("event","share",{method:e,content_type:"ai_humanization_result",item_id:"ghostlayer_share"})},b=async()=>{try{m&&navigator.clipboard&&(await navigator.clipboard.writeText("".concat(_.title," ").concat(t)),h(!0),setTimeout(()=>h(!1),2e3))}catch(e){console.error("Failed to copy link:",e)}},y=async()=>{if(m&&navigator.share)try{await navigator.share({title:_.title,text:_.description,url:t})}catch(e){console.error("Error sharing:",e)}};return(0,a.jsxs)("div",{className:w().socialShare,children:[(0,a.jsxs)("div",{className:w().header,children:[(0,a.jsx)("h3",{className:w().title,children:r?"\uD83C\uDF89 Share Your Success!":"\uD83D\uDCE2 Spread the Word"}),(0,a.jsx)("p",{className:w().subtitle,children:r?"Let others know how GhostLayer helped you bypass AI detection!":"Help others discover the best AI text humanizer"})]}),(0,a.jsxs)("div",{className:w().shareButtons,children:[(0,a.jsxs)("button",{className:"".concat(w().shareButton," ").concat(w().twitter),onClick:()=>N("twitter"),title:"Share on Twitter",children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:w().icon,children:(0,a.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})}),"Twitter"]}),(0,a.jsxs)("button",{className:"".concat(w().shareButton," ").concat(w().facebook),onClick:()=>N("facebook"),title:"Share on Facebook",children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:w().icon,children:(0,a.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Facebook"]}),(0,a.jsxs)("button",{className:"".concat(w().shareButton," ").concat(w().linkedin),onClick:()=>N("linkedin"),title:"Share on LinkedIn",children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:w().icon,children:(0,a.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})}),"LinkedIn"]}),(0,a.jsxs)("button",{className:"".concat(w().shareButton," ").concat(w().whatsapp),onClick:()=>N("whatsapp"),title:"Share on WhatsApp",children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:w().icon,children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"})}),"WhatsApp"]}),(0,a.jsxs)("button",{className:"".concat(w().shareButton," ").concat(w().reddit),onClick:()=>N("reddit"),title:"Share on Reddit",children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:w().icon,children:(0,a.jsx)("path",{d:"M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"})}),"Reddit"]}),(0,a.jsxs)("button",{className:"".concat(w().shareButton," ").concat(w().copyLink," ").concat(u?w().copied:""),onClick:b,title:"Copy Link",children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:w().icon,children:(0,a.jsx)("path",{d:"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"})}),u?"Copied!":"Copy Link"]}),m&&navigator.share&&(0,a.jsxs)("button",{className:"".concat(w().shareButton," ").concat(w().native),onClick:y,title:"Share",children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:w().icon,children:(0,a.jsx)("path",{d:"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"})}),"Share"]})]}),r&&(0,a.jsxs)("div",{className:w().sharePreview,children:[(0,a.jsx)("h4",{className:w().previewTitle,children:"Your shareable result:"}),(0,a.jsxs)("div",{className:w().previewContent,children:[(0,a.jsxs)("p",{className:w().previewText,children:['"',_.title,'"']}),(0,a.jsx)("div",{className:w().previewHashtags,children:_.hashtags.map((e,t)=>(0,a.jsxs)("span",{className:w().hashtag,children:["#",e]},t))})]})]})]})},T=s(7547),A=s.n(T),D=e=>{let{variant:t="default",showSocialShare:s=!0,customMessage:n=null,onCTAClick:o=null}=e,[r,c]=(0,i.useState)("floating"!==t),[l,d]=(0,i.useState)(null),[u,h]=(0,i.useState)(!1),[m,p]=(0,i.useState)(8);(0,i.useEffect)(()=>{if(h(!0),d(Math.floor(5e4*Math.random())+1e5),p(Math.floor(15*Math.random())+5),"floating"===t){let e=setTimeout(()=>c(!0),3e3);return()=>clearTimeout(e)}},[t]),(0,i.useEffect)(()=>{if(!u||!l)return;let e=setInterval(()=>{d(e=>e+Math.floor(100*Math.random())+50)},3e4);return()=>clearInterval(e)},[u,l]),(0,i.useEffect)(()=>{if(!u)return;let e=setInterval(()=>{p(Math.floor(15*Math.random())+5)},45e3);return()=>clearInterval(e)},[u]);let _=(()=>{switch(t){case"success":return{emoji:"\uD83C\uDF89",title:"Congratulations! Your Text is Now Undetectable",subtitle:"Join thousands who've successfully bypassed AI detection with GhostLayer",buttonText:"Humanize Another Text",shareMessage:"I just made my AI text completely undetectable with GhostLayer! \uD83D\uDD25 Try it free!"};case"floating":return{emoji:"\uD83D\uDE80",subtitle:"Join the AI text humanization revolution",buttonText:"Try GhostLayer Free",shareMessage:"Discovered the best AI text humanizer - GhostLayer bypasses detection 95% of the time!"};default:return{emoji:"\uD83D\uDC7B",title:"Ready to Make Your AI Text Undetectable?",subtitle:"Join 10,000+ content creators who trust GhostLayer",buttonText:"Start Humanizing Now",shareMessage:"Found the perfect AI text humanizer! GhostLayer makes AI content undetectable instantly."}}})(),x=n||_.shareMessage;return"floating"!==t||r?(0,a.jsxs)("div",{className:"".concat(A().viralCTA," ").concat(A()[t]," ").concat(r?A().visible:""),children:[(0,a.jsxs)("div",{className:A().container,children:[(0,a.jsxs)("div",{className:A().content,children:[(0,a.jsxs)("div",{className:A().header,children:[(0,a.jsx)("div",{className:A().emoji,children:_.emoji}),(0,a.jsx)("h2",{className:A().title,children:_.title}),(0,a.jsx)("p",{className:A().subtitle,children:_.subtitle})]}),(0,a.jsxs)("div",{className:A().stats,children:[(0,a.jsxs)("div",{className:A().stat,children:[(0,a.jsx)("div",{className:A().statNumber,children:"95%"}),(0,a.jsx)("div",{className:A().statLabel,children:"Success Rate"})]}),(0,a.jsxs)("div",{className:A().stat,children:[(0,a.jsx)("div",{className:A().statNumber,children:"2.3s"}),(0,a.jsx)("div",{className:A().statLabel,children:"Avg. Speed"})]})]}),(0,a.jsxs)("div",{className:A().actions,children:[(0,a.jsxs)("button",{className:A().primaryButton,onClick:()=>{o?o():window.scrollTo({top:0,behavior:"smooth"})},children:[_.buttonText,(0,a.jsx)("span",{className:A().buttonIcon,children:"→"})]}),"floating"!==t&&(0,a.jsxs)("div",{className:A().socialProof,children:[(0,a.jsxs)("div",{className:A().avatars,children:[(0,a.jsx)("div",{className:A().avatar,children:"\uD83D\uDC68‍\uD83D\uDCBC"}),(0,a.jsx)("div",{className:A().avatar,children:"\uD83D\uDC69‍\uD83C\uDF93"}),(0,a.jsx)("div",{className:A().avatar,children:"\uD83D\uDC68‍\uD83D\uDCBB"}),(0,a.jsx)("div",{className:A().avatar,children:"\uD83D\uDC69‍\uD83C\uDFEB"}),(0,a.jsx)("div",{className:A().avatar,children:"\uD83D\uDC68‍\uD83C\uDFA8"})]}),(0,a.jsxs)("p",{className:A().socialText,children:[(0,a.jsx)("strong",{children:"2,847 people"})," used GhostLayer in the last 24 hours"]})]})]}),s&&"floating"!==t&&(0,a.jsx)("div",{className:A().shareSection,children:(0,a.jsx)(S,{title:x,description:"Transform AI-generated text into human-like content that bypasses detection. Try it free!",hashtags:["AITextHumanizer","BypassAIDetection","GhostLayer","AIWriting"]})})]}),"floating"===t&&(0,a.jsx)("button",{className:A().closeButton,onClick:()=>c(!1),title:"Close",children:"\xd7"})]}),(0,a.jsxs)("div",{className:A().viralFeatures,children:[(0,a.jsxs)("div",{className:A().urgency,children:[(0,a.jsx)("span",{className:A().urgencyDot}),(0,a.jsxs)("span",{className:A().urgencyText,children:[u?m:8," people are using GhostLayer right now"]})]}),"default"===t&&(0,a.jsxs)("div",{className:A().testimonialQuote,children:[(0,a.jsx)("blockquote",{children:'"GhostLayer saved my content strategy. 95% bypass rate is incredible!"'}),(0,a.jsx)("cite",{children:"- Sarah M., Content Creator"})]})]})]}):null},I=s(1005),L=s(6853);let k=[{question:"How do I make AI text undetectable?",answer:"To make AI text undetectable, use GhostLayer's advanced humanization technology. Simply paste your AI-generated text, select your desired humanization level, and click 'Humanize'. Our 8-layer processing system transforms AI content into natural, human-like text that bypasses detection tools like GPTZero, Turnitin, and Originality.ai with a 95% success rate."},{question:"What is AI text humanization?",answer:"AI text humanization is the process of transforming AI-generated content to make it appear more natural and human-written. GhostLayer uses advanced algorithms to modify sentence structure, vocabulary, and writing patterns while preserving the original meaning and quality of your content."},{question:"Does GhostLayer work with ChatGPT, GPT-4, and Claude text?",answer:"Yes, GhostLayer works with text generated by all major AI models including ChatGPT, GPT-4, GPT-3.5, Claude, Bard, and other language models. Our humanization technology is specifically designed to handle content from any AI source and make it undetectable."},{question:"How accurate is GhostLayer at bypassing AI detection?",answer:"GhostLayer achieves a 95% success rate at bypassing AI detection tools. Our technology has been tested against popular detectors including GPTZero, Turnitin, Originality.ai, Copyleaks, and Winston AI. We continuously update our algorithms to stay ahead of detection improvements."},{question:"Is GhostLayer free to use?",answer:"Yes, GhostLayer offers a free tier with basic humanization features and limited daily usage. For unlimited processing, advanced features, and priority support, we offer premium plans starting at $9.99/month. You can try our service completely free without registration."},{question:"How long does it take to humanize AI text?",answer:"GhostLayer processes most text within 2-5 seconds. Processing time depends on text length and complexity. Our real-time processing ensures you get humanized results almost instantly, making it perfect for quick content creation workflows."},{question:"Will humanized text maintain the original meaning?",answer:"Absolutely. GhostLayer's advanced algorithms preserve the core meaning, context, and key information of your original text while making stylistic changes to bypass AI detection. Our quality assurance system ensures semantic integrity is maintained throughout the humanization process."},{question:"What file formats does GhostLayer support?",answer:"GhostLayer currently supports plain text input through our web interface. You can copy and paste text from any source including Word documents, Google Docs, PDFs, or any text editor. We're working on direct file upload support for future releases."},{question:"Can I use GhostLayer for academic writing?",answer:"While GhostLayer can humanize AI-generated text, we encourage users to follow their institution's academic integrity policies. Our tool is designed for content creators, marketers, and writers who need to produce natural-sounding content. Always check your school's AI usage guidelines."},{question:"How does GhostLayer compare to QuillBot and Paraphraser.io?",answer:"GhostLayer specifically focuses on AI detection bypass with 95% success rate, while QuillBot and Paraphraser.io are general paraphrasing tools. Our 8-layer humanization process is specifically designed to fool AI detectors, not just rephrase content. We offer superior detection bypass capabilities with better meaning preservation."}];var G=s(5202),B=s.n(G);function H(){let{data:e,status:t}=(0,n.useSession)(),[s,u]=(0,i.useState)(""),[h,p]=(0,i.useState)(""),[_,g]=(0,i.useState)(null),[f,j]=(0,i.useState)(!1),[N,C]=(0,i.useState)(""),[w,S]=(0,i.useState)(null),T=async()=>{if(!s.trim()){C("Please enter some text to process.");return}j(!0),C(""),p(""),g(null),S(null);let e=Date.now();try{let t=await (0,I.P)({text:s}),a=Date.now()-e;p(t.modifiedText),g(t.detectionResult),S({processingTime:a,originalLength:s.length,modifiedLength:t.modifiedText.length,wordsChanged:A(s,t.modifiedText)})}catch(e){C(e.message||"Failed to process text. Please try again."),console.error("Processing error:",e)}finally{j(!1)}},A=(e,t)=>{let s=e.trim().split(/\s+/),a=t.trim().split(/\s+/),i=0;for(let e=0;e<Math.min(s.length,a.length);e++)s[e]!==a[e]&&i++;return i+Math.abs(s.length-a.length)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Z,{title:"GhostLayer - AI Text Humanizer | Bypass AI Detection Instantly",description:"Transform AI-generated text into human-like content that bypasses AI detection. Free online tool with 95% success rate. Make ChatGPT, GPT-4, and Claude text undetectable instantly.",keywords:"AI text humanizer, bypass AI detection, make AI text undetectable, humanize ChatGPT text, AI content detector bypass, paraphrase AI text, undetectable AI writing, AI detection remover, ChatGPT humanizer, GPT detector bypass",canonicalUrl:"/",structuredData:[...(0,L.R7)(),(0,L.yi)(k),(0,L.zP)()]}),(0,a.jsxs)(o.Z,{children:[(0,a.jsx)("div",{className:B().app,children:(0,a.jsxs)("main",{className:B().main,children:[(0,a.jsx)("section",{className:B().hero,children:(0,a.jsxs)("div",{className:B().heroContent,children:[(0,a.jsxs)("h1",{className:B().heroTitle,children:["Transform AI Text to",(0,a.jsx)("span",{className:B().highlight,children:" Human-Like Content"})]}),(0,a.jsx)("p",{className:B().heroSubtitle,children:"Make your AI-generated text undetectable with our advanced paraphrasing and text modification technology. Free to use, instant results."}),(0,a.jsxs)("div",{className:B().heroStats,children:[(0,a.jsxs)("div",{className:B().stat,children:[(0,a.jsx)("span",{className:B().statNumber,children:"10K+"}),(0,a.jsx)("span",{className:B().statLabel,children:"Texts Processed"})]}),(0,a.jsxs)("div",{className:B().stat,children:[(0,a.jsx)("span",{className:B().statNumber,children:"95%"}),(0,a.jsx)("span",{className:B().statLabel,children:"Success Rate"})]}),(0,a.jsxs)("div",{className:B().stat,children:[(0,a.jsx)("span",{className:B().statNumber,children:"< 5s"}),(0,a.jsx)("span",{className:B().statLabel,children:"Processing Time"})]})]})]})}),(0,a.jsxs)("section",{className:B().processingSection,children:[(0,a.jsx)(r.Z,{inputText:s,onInputChange:e=>{u(e.target.value),N&&C("")},outputText:h,isLoading:f,onCopy:(e,t)=>{console.log("Copied ".concat(t," text:"),e.substring(0,50)+"...")},onDownload:e=>{let t=new Blob([e],{type:"text/plain"}),s=URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download="humanized-text.txt",document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)}}),(0,a.jsx)(c.Z,{onProcess:T,isLoading:f,disabled:!s.trim(),inputText:s}),N&&(0,a.jsxs)("div",{className:B().errorMessage,children:[(0,a.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,a.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor",strokeWidth:"2"}),(0,a.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor",strokeWidth:"2"})]}),(0,a.jsx)("span",{children:N})]}),w&&(0,a.jsxs)("div",{className:B().statsDisplay,children:[(0,a.jsx)("h3",{children:"Processing Results"}),(0,a.jsxs)("div",{className:B().statsGrid,children:[(0,a.jsxs)("div",{className:B().statItem,children:[(0,a.jsxs)("span",{className:B().statValue,children:[w.processingTime,"ms"]}),(0,a.jsx)("span",{className:B().statLabel,children:"Processing Time"})]}),(0,a.jsxs)("div",{className:B().statItem,children:[(0,a.jsx)("span",{className:B().statValue,children:w.wordsChanged}),(0,a.jsx)("span",{className:B().statLabel,children:"Words Modified"})]}),(0,a.jsxs)("div",{className:B().statItem,children:[(0,a.jsxs)("span",{className:B().statValue,children:[Math.round(w.wordsChanged/s.trim().split(/\s+/).length*100),"%"]}),(0,a.jsx)("span",{className:B().statLabel,children:"Change Rate"})]})]})]}),_&&(0,a.jsx)(l.Z,{outputText:h,detectionResult:_,isLoading:f})]})]})}),(0,a.jsx)(x,{}),(0,a.jsx)(b,{}),(0,a.jsx)(v,{}),(0,a.jsx)(m,{faqs:k,title:"How to Make AI Text Undetectable - FAQ"}),(0,a.jsx)(D,{variant:"default",showSocialShare:!0,customMessage:"Just discovered GhostLayer - the AI text humanizer that actually works! 95% bypass rate \uD83D\uDD25"}),(0,a.jsx)(D,{variant:"floating",showSocialShare:!1}),(0,a.jsx)(y.Z,{currentPage:"home"})]})]})}},4455:function(e){e.exports={benefitsSection:"BenefitsSection_benefitsSection__PhR9s",container:"BenefitsSection_container__mPmBw",benefitsContainer:"BenefitsSection_benefitsContainer__p1_Fb",useCasesContainer:"BenefitsSection_useCasesContainer__M7qt5",header:"BenefitsSection_header__W4baN",title:"BenefitsSection_title__3MlYg",subtitle:"BenefitsSection_subtitle__g7YUR",benefitsGrid:"BenefitsSection_benefitsGrid__l34HJ",benefitCard:"BenefitsSection_benefitCard__h77yl",benefitIcon:"BenefitsSection_benefitIcon__OvV68",benefitTitle:"BenefitsSection_benefitTitle__XULwK",benefitDescription:"BenefitsSection_benefitDescription__sT9ud",benefitStats:"BenefitsSection_benefitStats__ZNqc2",useCasesGrid:"BenefitsSection_useCasesGrid__h603p",useCaseCard:"BenefitsSection_useCaseCard__CfNm1",useCaseHeader:"BenefitsSection_useCaseHeader__vVUzf",useCaseIcon:"BenefitsSection_useCaseIcon__RfCjx",useCaseTitle:"BenefitsSection_useCaseTitle__chKR0",examplesList:"BenefitsSection_examplesList__7ON4w",example:"BenefitsSection_example__XmhF9",checkmark:"BenefitsSection_checkmark__scwoY",useCaseBenefit:"BenefitsSection_useCaseBenefit__aUMhn",statsSection:"BenefitsSection_statsSection__6o6Oi",statsTitle:"BenefitsSection_statsTitle__wUTsd",statsGrid:"BenefitsSection_statsGrid__d1mwH",statItem:"BenefitsSection_statItem__ISv_s",statNumber:"BenefitsSection_statNumber__1INz9",statLabel:"BenefitsSection_statLabel__B9eCI",statDetail:"BenefitsSection_statDetail__uS0F1"}},6261:function(e){e.exports={comparisonSection:"ComparisonSection_comparisonSection__jo7gy",container:"ComparisonSection_container__uKY3U",header:"ComparisonSection_header__x_fZ_",title:"ComparisonSection_title__B9SEx",subtitle:"ComparisonSection_subtitle__Lxy2O",tableContainer:"ComparisonSection_tableContainer__2Y8xS",comparisonTable:"ComparisonSection_comparisonTable__oYl5j",featureHeader:"ComparisonSection_featureHeader__yG6sD",competitorHeader:"ComparisonSection_competitorHeader__6s852",ourColumn:"ComparisonSection_ourColumn__vK5ff",competitorInfo:"ComparisonSection_competitorInfo__WGF_L",logo:"ComparisonSection_logo__qxIQI",name:"ComparisonSection_name__dh1DL",badge:"ComparisonSection_badge__mAoX0",featureRow:"ComparisonSection_featureRow__DTA1E",featureCell:"ComparisonSection_featureCell__f9hFc",valueCell:"ComparisonSection_valueCell__cCb2V",ourValue:"ComparisonSection_ourValue__tMm2A",highlight:"ComparisonSection_highlight__fkdDQ",stats:"ComparisonSection_stats__Ru_0L",stat:"ComparisonSection_stat__THFrS",statNumber:"ComparisonSection_statNumber__K7uuF",statLabel:"ComparisonSection_statLabel__4T_ug",statNote:"ComparisonSection_statNote__ttqXR",cta:"ComparisonSection_cta__ULhCm",ctaTitle:"ComparisonSection_ctaTitle__65GR5",ctaText:"ComparisonSection_ctaText__ADggz",ctaButton:"ComparisonSection_ctaButton__p2TtW"}},6331:function(e){e.exports={faqSection:"FAQ_faqSection__mcqBi",container:"FAQ_container__n6DQ4",title:"FAQ_title__ilb_5",faqList:"FAQ_faqList__p9htR",faqItem:"FAQ_faqItem__rwN1l",faqQuestion:"FAQ_faqQuestion__WikA7",active:"FAQ_active__aDisI",icon:"FAQ_icon__xt2ZR",faqAnswer:"FAQ_faqAnswer__AP5A8",open:"FAQ_open__3jmeP",answerContent:"FAQ_answerContent___dRKk"}},8582:function(e){e.exports={howToSection:"HowToGuide_howToSection__E79Wl",container:"HowToGuide_container__INvNa",header:"HowToGuide_header__wCLCR",title:"HowToGuide_title__n4Y3d",subtitle:"HowToGuide_subtitle__Pi1NU",stepsContainer:"HowToGuide_stepsContainer__2by1J",step:"HowToGuide_step__4zYrp",stepHeader:"HowToGuide_stepHeader__rraHs",stepNumber:"HowToGuide_stepNumber__8DpsX",number:"HowToGuide_number__9LYED",icon:"HowToGuide_icon__ZXnsk",stepContent:"HowToGuide_stepContent__759KO",stepTitle:"HowToGuide_stepTitle__gij9i",stepDescription:"HowToGuide_stepDescription__9iE_g",stepDetails:"HowToGuide_stepDetails__izwO2",detailsList:"HowToGuide_detailsList__Zii_M",detailItem:"HowToGuide_detailItem__3ezCI",checkmark:"HowToGuide_checkmark__lFgXv",stepConnector:"HowToGuide_stepConnector__AjNfn",connectorLine:"HowToGuide_connectorLine__F1BKw",connectorArrow:"HowToGuide_connectorArrow__0BZLn",bounce:"HowToGuide_bounce__HcWYc",cta:"HowToGuide_cta__T85JZ",ctaTitle:"HowToGuide_ctaTitle__Qb2Xm",ctaDescription:"HowToGuide_ctaDescription__QNCOP",ctaButton:"HowToGuide_ctaButton__kUrYh"}},2165:function(e){e.exports={socialShare:"SocialShare_socialShare__M7xpM",header:"SocialShare_header__TfcG5",title:"SocialShare_title___b4MX",subtitle:"SocialShare_subtitle__a_RnI",shareButtons:"SocialShare_shareButtons__Ez4uc",shareButton:"SocialShare_shareButton__9Lm8J",twitter:"SocialShare_twitter__AIRNP",facebook:"SocialShare_facebook__IqvjY",linkedin:"SocialShare_linkedin__2SX5d",whatsapp:"SocialShare_whatsapp__fsYmN",reddit:"SocialShare_reddit__nwpOA",copyLink:"SocialShare_copyLink___g4Pe",copied:"SocialShare_copied__PO8RJ",native:"SocialShare_native__UaMmc",icon:"SocialShare_icon__iQ5r3",sharePreview:"SocialShare_sharePreview___2OnO",previewTitle:"SocialShare_previewTitle__SOuFQ",previewContent:"SocialShare_previewContent__zyHdR",previewText:"SocialShare_previewText__1xCwh",previewHashtags:"SocialShare_previewHashtags__Vl8HB",hashtag:"SocialShare_hashtag__NJvWb",pulse:"SocialShare_pulse__WtwK5",loading:"SocialShare_loading__WFbg4",spin:"SocialShare_spin__Xs65_"}},7547:function(e){e.exports={viralCTA:"ViralCTA_viralCTA__tUa_P",floating:"ViralCTA_floating__3hDT7",visible:"ViralCTA_visible__XVhg5",success:"ViralCTA_success__gYZbK",container:"ViralCTA_container__p7lUu",content:"ViralCTA_content__fwGy_",header:"ViralCTA_header__heVLZ",emoji:"ViralCTA_emoji__iJWQN",bounce:"ViralCTA_bounce__m4g3e",title:"ViralCTA_title__QYTFW",subtitle:"ViralCTA_subtitle__iXFmo",stats:"ViralCTA_stats__Pm1Ll",stat:"ViralCTA_stat__zoME2",statNumber:"ViralCTA_statNumber__CcGz2",statLabel:"ViralCTA_statLabel__Z4EQ3",actions:"ViralCTA_actions__na7O6",primaryButton:"ViralCTA_primaryButton__EH8Bf",buttonIcon:"ViralCTA_buttonIcon__C_ij2",socialProof:"ViralCTA_socialProof__02zhX",avatars:"ViralCTA_avatars__saNId",avatar:"ViralCTA_avatar__KaaB4",socialText:"ViralCTA_socialText__k2hzr",shareSection:"ViralCTA_shareSection__OSpZ1",closeButton:"ViralCTA_closeButton___eELo",viralFeatures:"ViralCTA_viralFeatures__yf_eo",urgency:"ViralCTA_urgency__oBoYk",urgencyDot:"ViralCTA_urgencyDot__SUxkx",pulse:"ViralCTA_pulse__ezB4w",urgencyText:"ViralCTA_urgencyText__V3YTU",testimonialQuote:"ViralCTA_testimonialQuote__qfMXp"}}},function(e){e.O(0,[351,888,774,179],function(){return e(e.s=5557)}),_N_E=e.O()}]);