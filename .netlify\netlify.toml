functionsDirectory = "D:\\Project\\GhostLayer-2\\netlify\\functions"
functionsDirectoryOrigin = "default"
headersOrigin = "inline"
redirectsOrigin = "inline"

[build]
publish = "D:\\Project\\GhostLayer-2\\.next"
publishOrigin = "ui"
commandOrigin = "ui"
command = "npm run build"
functions = "D:\\Project\\GhostLayer-2\\netlify\\functions"

[build.environment]

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[functions]

[functions."*"]

[[plugins]]
origin = "default"
package = "@netlify/plugin-nextjs"

[plugins.inputs]

[[headers]]
for = "/_next/static/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
from = "/_next/image/"
to = "/.netlify/images?url=:url&w=:width&q=:quality"
status = 200.0
force = false

[redirects.query]
url = ":url"
w = ":width"
q = ":quality"

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/_ipx/*"
to = "/.netlify/images?url=:url&w=:width&q=:quality"
status = 200.0
force = false

[redirects.query]
url = ":url"
w = ":width"
q = ":quality"

[redirects.conditions]

[redirects.headers]