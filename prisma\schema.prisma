// prisma/schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite" // Start with SQLite for simplicity for MVP/development
  // For production, you would typically switch to a more robust database like PostgreSQL:
  // provider = "postgresql"
  // And update the DATABASE_URL accordingly in your .env file:
  // e.g., DATABASE_URL="postgresql://user:password@host:port/database?schema=public"
  url      = env("DATABASE_URL") // This will be read from the .env file
}

// Standard NextAuth.js models
// Official Prisma adapter schema: https://next-auth.js.org/adapters/prisma

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String // Type of account, e.g., "oauth", "email"
  provider          String // Name of the OAuth provider, e.g., "google", "github"
  providerAccountId String // User's ID as given by the provider
  refresh_token     String? // OAuth refresh token, potentially long
  access_token      String? // OAuth access token, potentially long
  expires_at        Int?    // Expiry timestamp for the access_token
  token_type        String? // Type of token, e.g., "Bearer"
  scope             String? // Scope granted by the user
  id_token          String? // JWT ID token, potentially long
  session_state     String? // Used by some providers

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId]) // Ensures a user can only link each provider account once
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique // The token used to identify the session
  userId       String
  expires      DateTime // When the session will expire
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid()) // Unique ID for the user (CUID is a good default)
  name          String?   // User's name, optional
  email         String?   @unique // User's email, should be unique if provided
  emailVerified DateTime? // Timestamp when the email was verified (for email-based auth)
  image         String?   // URL to the user's profile image

  // Relations to other NextAuth.js models
  accounts      Account[]
  sessions      Session[]
  subscriptions Subscription[] // Added: Relation to Subscription model (a user can have multiple subscriptions over time)
  styleProfiles StyleProfile[] // Added: Relation to StyleProfile model (a user can have multiple style profiles)

  // --- Application-specific fields ---
  // This field will likely be updated by webhooks based on the active subscription's status and plan.
  subscriptionTier String @default("free") // e.g., "free", "premium_monthly", "premium_yearly"

  // usageCredits might be tied to the subscription plan or managed separately.
  // If tied to subscription, it could be reset when a subscription renews.
  usageCredits     Int    @default(10)    // Example: number of paraphrases or other actions allowed

  // Store the customer ID from the payment provider (e.g., Stripe)
  // This is useful for linking the user to their customer record in Stripe.
  stripeCustomerId String? @unique

  // Timestamps
  createdAt DateTime @default(now()) // When the user record was created
  updatedAt DateTime @updatedAt     // When the user record was last updated
}

model VerificationToken {
  identifier String   // Typically the email address for passwordless/email verification
  token      String   @unique // The verification token
  expires    DateTime // When the token will expire

  @@unique([identifier, token]) // Ensures identifier/token pair is unique
}

// New Subscription Model
// Stores details about user subscriptions, typically managed via a payment provider like Stripe.
model Subscription {
  id                       String   @id @default(cuid()) // Unique ID for the subscription record

  userId                   String   // Foreign key to the User model
  user                     User     @relation(fields: [userId], references: [id], onDelete: Cascade) // Relation to the User

  // Stripe (or other payment provider) specific IDs
  stripeSubscriptionId     String   @unique // The ID of the subscription object in Stripe. This should be unique.
  stripeCustomerId         String   // The ID of the customer object in Stripe. (Often same as User.stripeCustomerId but good to have here for direct subscription context)
  stripePriceId            String   // The ID of the Stripe Price object, representing the specific plan (e.g., premium_monthly_plan_id)

  // Subscription status and timing
  // This status is typically updated via webhooks from the payment provider (e.g., Stripe).
  status                   String   // e.g., "active", "canceled", "past_due", "incomplete", "trialing", "unpaid"
  stripeCurrentPeriodEnd   DateTime // Timestamp indicating when the current billing period (paid for by the user) ends.
                                    // For active subscriptions, Stripe will attempt renewal around this time.

  // Timestamps
  createdAt                DateTime @default(now()) // When this subscription record was created in our DB
  updatedAt                DateTime @updatedAt     // When this subscription record was last updated in our DB

  // Indexing for common queries
  @@index([userId]) // Useful for quickly finding all subscriptions for a user
}

// Writing Style Profile Model
// Stores user's writing style analysis and preferences for personalized humanization
model StyleProfile {
  id                    String   @id @default(cuid()) // Unique ID for the style profile

  userId                String   // Foreign key to the User model
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade) // Relation to the User

  // Profile metadata
  name                  String   // User-defined name for this style profile (e.g., "Academic Writing", "Casual Blog")
  description           String?  // Optional description of the writing style
  isActive              Boolean  @default(false) // Whether this is the currently active style profile

  // Style analysis data (stored as JSON for flexibility)
  sentencePatterns      String   // JSON string containing sentence pattern analysis
  vocabularyComplexity  String   // JSON string containing vocabulary analysis
  transitionPhrases     String   // JSON string containing transition phrase preferences
  punctuationStyle      String   // JSON string containing punctuation usage patterns
  personalExpressions   String   // JSON string containing personal expressions and markers
  writingQuirks         String   // JSON string containing identified writing quirks
  toneAnalysis          String   // JSON string containing tone and emotional patterns

  // Style application settings
  defaultStrength       Int      @default(50) // Default style strength (0-100)

  // Sample metadata
  sampleCount           Int      @default(0)  // Number of writing samples used to create this profile
  totalSampleWords      Int      @default(0)  // Total word count of all samples

  // Timestamps
  createdAt             DateTime @default(now()) // When this style profile was created
  updatedAt             DateTime @updatedAt     // When this style profile was last updated
  lastUsedAt            DateTime? // When this style profile was last used for humanization

  // Indexing for common queries
  @@index([userId]) // Useful for quickly finding all style profiles for a user
  @@index([userId, isActive]) // Useful for finding the active style profile for a user
}
