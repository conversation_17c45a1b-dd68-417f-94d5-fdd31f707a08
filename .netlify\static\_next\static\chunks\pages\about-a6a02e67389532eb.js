(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[521],{512:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/about",function(){return a(1120)}])},1120:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return u}});var n=a(5893);a(7294),a(9008);var s=a(6993),i=a(2458),o=a(4164),c=a(6853),r=a(5038),l=a.n(r);function u(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.Z,{title:"About GhostLayer | Leading AI Text Humanization Technology",description:"Learn about GhostLay<PERSON>'s mission to revolutionize AI content creation. Our advanced algorithms make AI-generated text undetectable while preserving quality and meaning.",keywords:"about <PERSON><PERSON><PERSON><PERSON>, AI text humanization company, AI detection bypass technology, undetectable AI content, AI writing tools company",canonicalUrl:"/about",structuredData:[(0,c.hB)(),(0,c.Kx)("About GhostLayer | Leading AI Text Humanization Technology","Learn about GhostLayer's mission to revolutionize AI content creation","2024-01-01",new Date().toISOString().split("T")[0])]}),(0,n.jsx)(s.Z,{children:(0,n.jsxs)("div",{className:l().container,children:[(0,n.jsxs)("header",{className:l().header,children:[(0,n.jsx)("h1",{className:l().title,children:"About GhostLayer"}),(0,n.jsx)("p",{className:l().subtitle,children:"Making AI-generated content indistinguishable from human writing"})]}),(0,n.jsx)("section",{className:l().missionSection,children:(0,n.jsxs)("div",{className:l().content,children:[(0,n.jsx)("h2",{className:l().sectionTitle,children:"Our Mission"}),(0,n.jsx)("p",{className:l().text,children:"GhostLayer was created to bridge the gap between AI-generated content and natural human writing. As AI tools become more prevalent, we recognized the need for a solution that could transform robotic, detectable AI text into natural, engaging content that resonates with human readers."}),(0,n.jsx)("p",{className:l().text,children:"Our advanced algorithms analyze text patterns, sentence structures, and linguistic nuances to create content that not only bypasses AI detection tools but also improves readability and maintains the original meaning and intent."})]})}),(0,n.jsxs)("section",{className:l().valuesSection,children:[(0,n.jsx)("h2",{className:l().sectionTitle,children:"Our Values"}),(0,n.jsxs)("div",{className:l().valuesGrid,children:[(0,n.jsxs)("div",{className:l().valueCard,children:[(0,n.jsx)("div",{className:l().valueIcon,children:"\uD83C\uDFAF"}),(0,n.jsx)("h3",{className:l().valueTitle,children:"Quality First"}),(0,n.jsx)("p",{className:l().valueDescription,children:"We prioritize content quality and meaning preservation above all else."})]}),(0,n.jsxs)("div",{className:l().valueCard,children:[(0,n.jsx)("div",{className:l().valueIcon,children:"\uD83D\uDD12"}),(0,n.jsx)("h3",{className:l().valueTitle,children:"Privacy Focused"}),(0,n.jsx)("p",{className:l().valueDescription,children:"Your content is never stored or shared. Complete privacy guaranteed."})]}),(0,n.jsxs)("div",{className:l().valueCard,children:[(0,n.jsx)("div",{className:l().valueIcon,children:"⚡"}),(0,n.jsx)("h3",{className:l().valueTitle,children:"Speed & Efficiency"}),(0,n.jsx)("p",{className:l().valueDescription,children:"Fast processing without compromising on quality or accuracy."})]}),(0,n.jsxs)("div",{className:l().valueCard,children:[(0,n.jsx)("div",{className:l().valueIcon,children:"\uD83C\uDF1F"}),(0,n.jsx)("h3",{className:l().valueTitle,children:"Innovation"}),(0,n.jsx)("p",{className:l().valueDescription,children:"Continuously improving our algorithms to stay ahead of detection tools."})]})]})]}),(0,n.jsxs)("section",{className:l().teamSection,children:[(0,n.jsx)("h2",{className:l().sectionTitle,children:"Our Team"}),(0,n.jsxs)("div",{className:l().teamContent,children:[(0,n.jsx)("p",{className:l().text,children:"GhostLayer is developed by a team of AI researchers, linguists, and software engineers passionate about natural language processing and content creation. Our diverse backgrounds in machine learning, computational linguistics, and user experience design enable us to create tools that are both powerful and user-friendly."}),(0,n.jsx)("p",{className:l().text,children:"We're committed to ethical AI use and believe that technology should enhance human creativity rather than replace it. GhostLayer empowers content creators to leverage AI assistance while maintaining authenticity and originality."})]})]}),(0,n.jsxs)("section",{className:l().contactSection,children:[(0,n.jsx)("h2",{className:l().sectionTitle,children:"Get in Touch"}),(0,n.jsx)("p",{className:l().text,children:"Have questions, feedback, or suggestions? We'd love to hear from you."}),(0,n.jsxs)("div",{className:l().contactInfo,children:[(0,n.jsxs)("div",{className:l().contactItem,children:[(0,n.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,n.jsxs)("div",{className:l().contactItem,children:[(0,n.jsx)("strong",{children:"GitHub:"}),(0,n.jsx)("a",{href:"https://github.com/HectorTa1989/stealthwriter-ai",target:"_blank",rel:"noopener noreferrer",children:"HectorTa1989/stealthwriter-ai"})]})]})]}),(0,n.jsx)(o.Z,{currentPage:"about"})]})})]})}},5038:function(e){e.exports={container:"About_container__W2caq",header:"About_header__B6ZqC",title:"About_title__UwDJe",subtitle:"About_subtitle__RqpG_",missionSection:"About_missionSection__GY0z1",teamSection:"About_teamSection__C4koO",contactSection:"About_contactSection__PHbeC",valuesSection:"About_valuesSection__ORGGj",content:"About_content__mzLG9",sectionTitle:"About_sectionTitle___6frd",text:"About_text__ty6Um",valuesGrid:"About_valuesGrid___0hZj",valueCard:"About_valueCard__dxbAG",valueIcon:"About_valueIcon__mvXYO",valueTitle:"About_valueTitle__nDZ3S",valueDescription:"About_valueDescription__LIFfP",teamContent:"About_teamContent__CD14C",contactInfo:"About_contactInfo__3VxM5",contactItem:"About_contactItem__QADT_"}}},function(e){e.O(0,[351,888,774,179],function(){return e(e.s=512)}),_N_E=e.O()}]);