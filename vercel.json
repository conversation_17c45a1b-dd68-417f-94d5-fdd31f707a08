{"version": 2, "name": "stealthwriter-ai", "alias": ["stealthwriter-ai"], "regions": ["iad1"], "build": {"env": {"NODE_ENV": "production"}}, "env": {"NODE_ENV": "production"}, "functions": {"src/pages/api/**/*.js": {"maxDuration": 30}, "src/pages/api/stripe/webhook.js": {"maxDuration": 10}, "src/pages/api/process.js": {"maxDuration": 60}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://stealthwriter-ai.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "rewrites": [{"source": "/health", "destination": "/api/health"}], "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}]}