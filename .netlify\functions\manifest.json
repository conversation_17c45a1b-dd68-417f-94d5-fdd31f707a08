{"functions": [{"bundler": "zisi", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "D:\\Project\\GhostLayer-2\\netlify\\functions\\auth.js", "name": "auth", "priority": 10, "runtimeVersion": "nodejs20.x", "path": "D:\\Project\\GhostLayer-2\\.netlify\\functions\\auth.zip", "runtime": "js"}, {"bundler": "zisi", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "D:\\Project\\GhostLayer-2\\netlify\\functions\\health.js", "name": "health", "priority": 10, "runtimeVersion": "nodejs20.x", "path": "D:\\Project\\GhostLayer-2\\.netlify\\functions\\health.zip", "runtime": "js"}, {"bundler": "zisi", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "D:\\Project\\GhostLayer-2\\netlify\\functions\\process.js", "name": "process", "priority": 10, "runtimeVersion": "nodejs20.x", "path": "D:\\Project\\GhostLayer-2\\.netlify\\functions\\process.zip", "runtime": "js"}, {"bundler": "zisi", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "D:\\Project\\GhostLayer-2\\netlify\\functions\\test-detection.js", "name": "test-detection", "priority": 10, "runtimeVersion": "nodejs20.x", "path": "D:\\Project\\GhostLayer-2\\.netlify\\functions\\test-detection.zip", "runtime": "js"}, {"bundler": "none", "displayName": "Next.js <PERSON>", "generator": "@netlify/plugin-nextjs@5.11.5", "invocationMode": "stream", "buildData": {"bootstrapVersion": "2.1.2", "runtimeAPIVersion": 2}, "mainFile": "D:\\Project\\GhostLayer-2\\.netlify\\functions-internal\\___netlify-server-handler\\___netlify-server-handler.mjs", "name": "___netlify-server-handler", "priority": 0, "runtimeVersion": "nodejs20.x", "path": "D:\\Project\\GhostLayer-2\\.netlify\\functions\\___netlify-server-handler.zip", "runtime": "js", "routes": [{"pattern": "/*", "expression": "^(?:\\/(.*))\\/?$", "methods": [], "prefer_static": true}]}], "system": {"arch": "x64", "platform": "win32"}, "timestamp": 1752293081807, "version": 1}