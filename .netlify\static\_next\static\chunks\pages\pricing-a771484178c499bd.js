(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[939],{4223:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/pricing",function(){return t(9337)}])},9337:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return k}});var n,i=t(5893),a=t(7294),o=t(3299),s=t(1163),c=t(6993),l=t(2458),u=t(4164),d=t(6853),p=t(614),h=t.n(p),m="https://js.stripe.com/v3",f=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,g=function(){for(var e=document.querySelectorAll('script[src^="'.concat(m,'"]')),r=0;r<e.length;r++){var t=e[r];if(f.test(t.src))return t}return null},v=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",t=document.createElement("script");t.src="".concat(m).concat(r);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(t),t},w=function(e,r){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"2.4.0",startTime:r})},y=null,P=null,j=null,S=function(e,r,t){if(null===e)return null;var n=e.apply(void 0,r);return w(n,t),n},E=!1,I=function(){return n||(n=(null!==y?y:(y=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,n=g();n?n&&null!==j&&null!==P&&(n.removeEventListener("load",j),n.removeEventListener("error",P),null===(t=n.parentNode)||void 0===t||t.removeChild(n),n=v(null)):n=v(null),j=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},P=function(){r(Error("Failed to load Stripe.js"))},n.addEventListener("load",j),n.addEventListener("error",P)}catch(e){r(e);return}})).catch(function(e){return y=null,Promise.reject(e)})).catch(function(e){return n=null,Promise.reject(e)}))};Promise.resolve().then(function(){return I()}).catch(function(e){E||console.warn(e)});var b=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];E=!0;var n=Date.now();return I().then(function(e){return S(e,r,n)})};let x=null;var _=()=>{let e="pk_test_your_stripe_publishable_key";return x||(e||console.error("CRITICAL: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set for production environment."),x=b(e)),x},C=t(3454);function k(){var e;let{data:r,status:t}=(0,o.useSession)(),n=(0,s.useRouter)(),[p,m]=(0,a.useState)(null),[f,g]=(0,a.useState)(!1),[v,w]=(0,a.useState)(""),[y,P]=(0,a.useState)(null);(0,a.useEffect)(()=>{var e;(null==r?void 0:null===(e=r.user)||void 0===e?void 0:e.id)&&!p&&!f&&(g(!0),w(""),fetch("/api/user-profile").then(async e=>{if(!e.ok){let r=await e.json().catch(()=>({}));throw Error(r.message||r.error||"Error ".concat(e.status,": Failed to load user profile."))}return e.json()}).then(e=>m(e)).catch(e=>{console.error("Failed to fetch user profile for pricing page:",e),w("Could not load your profile: ".concat(e.message,". Please try refreshing."))}).finally(()=>g(!1)))},[r,p,f]);let j=async e=>{if(!r){n.push("/api/auth/signin?callbackUrl=".concat(encodeURIComponent("/pricing")));return}if(!e){w("The selected plan is not available at the moment. Please try again later."),console.error("handleUpgradeClick: priceId is missing.");return}P(e),w("");try{let r=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:e})});if(!r.ok){let e=await r.json().catch(()=>({message:"Failed to initiate checkout. Please try again."}));throw Error(e.message||e.error||"Error: ".concat(r.status))}let{sessionId:t}=await r.json();if(!t)throw Error("Checkout session ID not received from server.");let n=await _();if(!n)throw Error("Stripe.js failed to load. Please check your internet connection or ad-blocker.");let{error:i}=await n.redirectToCheckout({sessionId:t});i&&(console.error("Stripe redirectToCheckout error:",i.message),w("Error redirecting to checkout: ".concat(i.message)))}catch(e){console.error("Upgrade process error:",e.message),w("An error occurred: ".concat(e.message))}finally{P(null)}},S=async()=>{P("manage"),w("");try{let e=await fetch("/api/stripe/create-customer-portal-session",{method:"POST"});if(!e.ok){let r=await e.json().catch(()=>({}));throw Error(r.message||r.error||"Error ".concat(e.status))}let{url:r}=await e.json();if(r)window.location.href=r;else throw Error("Customer portal URL not received.")}catch(e){console.error("Manage subscription error:",e.message),w("Could not open subscription management: ".concat(e.message))}finally{P(null)}},E=(null==p?void 0:p.subscriptionTier)||(null==r?void 0:null===(e=r.user)||void 0===e?void 0:e.subscriptionTier)||"free",I=C.env.NEXT_PUBLIC_PREMIUM_PLAN_PRICE_ID||null;return"loading"===t||r&&f&&!p&&!v?(0,i.jsx)(c.Z,{children:(0,i.jsx)("div",{className:h().loadingMessage,children:"Loading user data..."})}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(l.Z,{title:"Pricing Plans | AI Text Humanizer - GhostLayer",description:"Choose the perfect plan for your AI text humanization needs. Free tier available. Premium plans with unlimited processing, advanced features, and priority support.",keywords:"AI text humanizer pricing, AI detection bypass plans, ChatGPT humanizer cost, AI content tools pricing, undetectable AI writing subscription",canonicalUrl:"/pricing",structuredData:(0,d._t)()}),(0,i.jsx)(c.Z,{children:(0,i.jsxs)("div",{className:h().pricingContainer,children:[(0,i.jsx)("h1",{className:h().pageTitle,children:"Choose Your Plan"}),(0,i.jsx)("p",{className:h().pageSubtitle,children:"Supercharge your writing with our Premium features, higher limits, and an ad-free experience."}),v&&(0,i.jsx)("p",{className:h().errorMessage,children:v}),(0,i.jsx)("div",{className:h().plansGrid,children:[{name:"Freemium",price:"$0",pricePer:"/ month",features:["Basic paraphrasing quality","Limited daily usage (e.g., 5-10 requests)","Shorter input text length (e.g., 300-500 words)","Standard processing speed","Ad-supported experience","Community support"],tierIdentifier:"free"},{name:"Premium",price:"$9.99",pricePer:"/ month",stripePriceId:I,features:["Advanced paraphrasing (PEGASUS model)","Significantly higher daily usage (e.g., 100-200 requests)","Longer input text length (e.g., 1500-3000 words)","Priority processing (conceptual)","Ad-free experience","More fine-grained controls (future)","Access to processing history (future)","Priority email support (future)"],tierIdentifier:"premium"}].map(e=>{let t=E===e.tierIdentifier,a=y===e.stripePriceId||t&&"manage"===y,o="Choose Plan",s=()=>{},c=null!==y;return"free"===e.tierIdentifier?t?(o="Your Current Plan",c=!0):r?(o="Downgrade (via Portal)",s=S):(o="Sign Up for Free",s=()=>n.push("/api/auth/signin?callbackUrl=".concat(encodeURIComponent("/pricing")))):t?(o="Manage Subscription",s=S):(o="Upgrade to ".concat(e.name),s=()=>j(e.stripePriceId),e.stripePriceId||(c=!0)),a&&(o="Processing..."),(0,i.jsxs)("div",{className:"".concat(h().planCard," ").concat(t?h().currentPlan:""),children:[(0,i.jsx)("h2",{className:h().planTitle,children:e.name}),(0,i.jsxs)("p",{className:h().planPrice,children:[e.price," ",(0,i.jsx)("span",{className:h().pricePer,children:e.pricePer})]}),(0,i.jsx)("ul",{className:h().featuresList,children:e.features.map((e,r)=>(0,i.jsx)("li",{children:e},r))}),(0,i.jsx)("button",{onClick:s,className:"".concat(h().planButton," ").concat("free"===e.tierIdentifier||t?"":h().upgradeButton),disabled:c,children:o}),"free"!==e.tierIdentifier&&!e.stripePriceId&&(0,i.jsx)("p",{className:h().warningMessage,children:"This plan is not available for purchase at this moment. Please check back later."})]},e.name)})}),(0,i.jsx)("p",{style:{marginTop:"2rem",fontSize:"0.9rem",color:"#777"},children:"Payments are securely processed by Stripe. You can manage your subscription at any time."}),(0,i.jsx)(u.Z,{currentPage:"pricing"})]})})]})}}},function(e){e.O(0,[351,888,774,179],function(){return e(e.s=4223)}),_N_E=e.O()}]);