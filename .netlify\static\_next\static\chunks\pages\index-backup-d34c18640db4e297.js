(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[804],{6376:function(e,s,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/index-backup",function(){return a(9317)}])},9317:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return N}});var n=a(5893),t=a(7294),i=a(9008),r=a.n(i),l=a(3299),o=a(1664),c=a.n(o),d=a(4998),h=a.n(d),u=()=>{let{data:e,status:s}=(0,l.useSession)(),[a,i]=(0,t.useState)(!1);return(0,n.jsx)("header",{className:h().header,children:(0,n.jsxs)("div",{className:h().container,children:[(0,n.jsxs)("div",{className:h().nav,children:[(0,n.jsxs)(c(),{href:"/",className:h().logo,children:[(0,n.jsx)("div",{className:h().logoIcon,children:(0,n.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",children:[(0,n.jsx)("rect",{width:"32",height:"32",rx:"8",fill:"url(#gradient)"}),(0,n.jsx)("path",{d:"M8 12h16M8 16h16M8 20h12",stroke:"white",strokeWidth:"2",strokeLinecap:"round"}),(0,n.jsx)("defs",{children:(0,n.jsxs)("linearGradient",{id:"gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,n.jsx)("stop",{offset:"0%",stopColor:"#0ea5e9"}),(0,n.jsx)("stop",{offset:"100%",stopColor:"#0369a1"})]})})]})}),(0,n.jsx)("span",{className:h().logoText,children:"GhostLayer"})]}),(0,n.jsxs)("nav",{className:h().desktopNav,children:[(0,n.jsx)(c(),{href:"/",className:h().navLink,children:"Home"}),(0,n.jsx)(c(),{href:"/features",className:h().navLink,children:"Features"}),(0,n.jsx)(c(),{href:"/pricing",className:h().navLink,children:"Pricing"}),(0,n.jsx)(c(),{href:"/about",className:h().navLink,children:"About"})]}),(0,n.jsxs)("div",{className:h().userActions,children:["loading"===s?(0,n.jsx)("div",{className:h().loadingSpinner}):e?(0,n.jsxs)("div",{className:h().userMenu,children:[(0,n.jsxs)("div",{className:h().userInfo,children:[e.user.image&&(0,n.jsx)("img",{src:e.user.image,alt:e.user.name,className:h().userAvatar}),(0,n.jsxs)("div",{className:h().userDetails,children:[(0,n.jsx)("span",{className:h().userName,children:e.user.name}),(0,n.jsx)("span",{className:h().userTier,children:"premium_monthly"===e.user.subscriptionTier||"premium_yearly"===e.user.subscriptionTier?"Premium":"Free"})]})]}),(0,n.jsx)("button",{onClick:()=>(0,l.signOut)(),className:h().signOutButton,children:"Sign Out"})]}):(0,n.jsx)("div",{className:h().authButtons,children:(0,n.jsxs)("button",{onClick:()=>(0,l.signIn)("google"),className:h().signInButton,children:[(0,n.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",className:h().googleIcon,children:[(0,n.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,n.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,n.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,n.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Sign in with Google"]})}),(0,n.jsx)("button",{className:h().mobileMenuButton,onClick:()=>{i(!a)},"aria-label":"Toggle menu",children:(0,n.jsxs)("span",{className:"".concat(h().hamburger," ").concat(a?h().open:""),children:[(0,n.jsx)("span",{}),(0,n.jsx)("span",{}),(0,n.jsx)("span",{})]})})]})]}),(0,n.jsxs)("nav",{className:"".concat(h().mobileNav," ").concat(a?h().open:""),children:[(0,n.jsx)(c(),{href:"/",className:h().mobileNavLink,onClick:()=>i(!1),children:"Home"}),(0,n.jsx)(c(),{href:"/features",className:h().mobileNavLink,onClick:()=>i(!1),children:"Features"}),(0,n.jsx)(c(),{href:"/pricing",className:h().mobileNavLink,onClick:()=>i(!1),children:"Pricing"}),(0,n.jsx)(c(),{href:"/about",className:h().mobileNavLink,onClick:()=>i(!1),children:"About"}),!e&&(0,n.jsx)("button",{onClick:()=>{(0,l.signIn)("google"),i(!1)},className:h().mobileSignInButton,children:"Sign in with Google"})]})]})})},m=a(2377),x=a(1616),g=a(5337),j=a(1005),_=a(5202),p=a.n(_);function N(){var e;let{data:s,status:a}=(0,l.useSession)(),[i,o]=(0,t.useState)(""),[c,d]=(0,t.useState)(""),[h,_]=(0,t.useState)(null),[N,v]=(0,t.useState)(!1),[f,b]=(0,t.useState)(""),[k,H]=(0,t.useState)(null),L=async()=>{if(!i.trim()){b("Please enter some text to process.");return}v(!0),b(""),d(""),_(null),H(null);let e=Date.now();try{let s=await (0,j.P)({text:i}),a=Date.now()-e;d(s.modifiedText),_(s.detectionResult),H({processingTime:a,originalLength:i.length,modifiedLength:s.modifiedText.length,wordsChanged:T(i,s.modifiedText)})}catch(e){b(e.message||"Failed to process text. Please try again."),console.error("Processing error:",e)}finally{v(!1)}},T=(e,s)=>{let a=e.trim().split(/\s+/),n=s.trim().split(/\s+/),t=0;for(let e=0;e<Math.min(a.length,n.length);e++)a[e]!==n[e]&&t++;return t+Math.abs(a.length-n.length)};return"loading"===a||s&&(null===(e=s.user)||void 0===e||e.subscriptionTier),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(r(),{children:[(0,n.jsx)("title",{children:"GhostLayer - Transform AI Text to Human-Like Content"}),(0,n.jsx)("meta",{name:"description",content:"Transform AI-generated text into human-like content that bypasses AI detection. Free online tool with advanced paraphrasing and text modification."}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("meta",{property:"og:title",content:"GhostLayer - Transform AI Text to Human-Like Content"}),(0,n.jsx)("meta",{property:"og:description",content:"Transform AI-generated text into human-like content that bypasses AI detection."}),(0,n.jsx)("meta",{property:"og:type",content:"website"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,n.jsxs)("div",{className:p().app,children:[(0,n.jsx)(u,{}),(0,n.jsxs)("main",{className:p().main,children:[(0,n.jsx)("section",{className:p().hero,children:(0,n.jsxs)("div",{className:p().heroContent,children:[(0,n.jsxs)("h1",{className:p().heroTitle,children:["Transform AI Text to",(0,n.jsx)("span",{className:p().highlight,children:" Human-Like Content"})]}),(0,n.jsx)("p",{className:p().heroSubtitle,children:"Make your AI-generated text undetectable with our advanced paraphrasing and text modification technology. Free to use, instant results."}),(0,n.jsxs)("div",{className:p().heroStats,children:[(0,n.jsxs)("div",{className:p().stat,children:[(0,n.jsx)("span",{className:p().statNumber,children:"10K+"}),(0,n.jsx)("span",{className:p().statLabel,children:"Texts Processed"})]}),(0,n.jsxs)("div",{className:p().stat,children:[(0,n.jsx)("span",{className:p().statNumber,children:"95%"}),(0,n.jsx)("span",{className:p().statLabel,children:"Success Rate"})]}),(0,n.jsxs)("div",{className:p().stat,children:[(0,n.jsx)("span",{className:p().statNumber,children:"< 5s"}),(0,n.jsx)("span",{className:p().statLabel,children:"Processing Time"})]})]})]})}),(0,n.jsxs)("section",{className:p().processingSection,children:[(0,n.jsx)(m.Z,{inputText:i,onInputChange:e=>{o(e.target.value),f&&b("")},outputText:c,isLoading:N,onCopy:(e,s)=>{console.log("Copied ".concat(s," text:"),e.substring(0,50)+"...")},onDownload:e=>{let s=new Blob([e],{type:"text/plain"}),a=URL.createObjectURL(s),n=document.createElement("a");n.href=a,n.download="humanized-text.txt",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(a)}}),(0,n.jsx)(x.Z,{onProcess:L,isLoading:N,disabled:!i.trim(),inputText:i}),f&&(0,n.jsxs)("div",{className:p().errorMessage,children:[(0,n.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor",strokeWidth:"2"}),(0,n.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor",strokeWidth:"2"})]}),(0,n.jsx)("span",{children:f})]}),k&&(0,n.jsxs)("div",{className:p().statsDisplay,children:[(0,n.jsx)("h3",{children:"Processing Results"}),(0,n.jsxs)("div",{className:p().statsGrid,children:[(0,n.jsxs)("div",{className:p().statItem,children:[(0,n.jsxs)("span",{className:p().statValue,children:[k.processingTime,"ms"]}),(0,n.jsx)("span",{className:p().statLabel,children:"Processing Time"})]}),(0,n.jsxs)("div",{className:p().statItem,children:[(0,n.jsx)("span",{className:p().statValue,children:k.wordsChanged}),(0,n.jsx)("span",{className:p().statLabel,children:"Words Modified"})]}),(0,n.jsxs)("div",{className:p().statItem,children:[(0,n.jsxs)("span",{className:p().statValue,children:[Math.round(k.wordsChanged/i.trim().split(/\s+/).length*100),"%"]}),(0,n.jsx)("span",{className:p().statLabel,children:"Change Rate"})]})]})]}),h&&(0,n.jsx)(g.Z,{outputText:c,detectionResult:h,isLoading:N})]})]})]})]})}},4998:function(e){e.exports={header:"Header_header__hO3lJ",container:"Header_container__EZyBV",nav:"Header_nav__3fHSH",logo:"Header_logo__e5KhT",logoIcon:"Header_logoIcon__VF71g",logoText:"Header_logoText__4ZhAR",desktopNav:"Header_desktopNav__9ddFh",navLink:"Header_navLink__LzXns",userActions:"Header_userActions__fTgN8",loadingSpinner:"Header_loadingSpinner__6xJj9",spin:"Header_spin__UL_47",userMenu:"Header_userMenu__XfrJB",userInfo:"Header_userInfo__dkUZi",userAvatar:"Header_userAvatar__2ylfw",userDetails:"Header_userDetails__v_DF2",userName:"Header_userName__y5UKs",userTier:"Header_userTier__I2kOa",authButtons:"Header_authButtons__yEIBJ",signInButton:"Header_signInButton__Ilk0l",googleIcon:"Header_googleIcon__us48t",signOutButton:"Header_signOutButton__OJDus",mobileMenuButton:"Header_mobileMenuButton__i228a",hamburger:"Header_hamburger__lUulX",open:"Header_open__u7Smf",mobileNav:"Header_mobileNav__HHzTb",mobileNavLink:"Header_mobileNavLink__cGBRQ",mobileSignInButton:"Header_mobileSignInButton__jf6vK"}}},function(e){e.O(0,[351,888,774,179],function(){return e(e.s=6376)}),_N_E=e.O()}]);