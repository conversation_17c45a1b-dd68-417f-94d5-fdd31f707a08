// netlify/functions/process.js
// Main text processing function for Netlify

const { addControlledMistakes, changeStyle, simpleParaphrase } = require('../../src/utils/textModifiers');
const { checkWithGPTZero } = require('../../src/services/gptzeroClient');
const { paraphraseWithPegasus } = require('../../src/services/paraphraseService');
const { balancedHumanization, qualityCheck } = require('../../src/utils/balancedHumanizer');

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    try {
        const { text, styleProfile, styleStrength } = JSON.parse(event.body);

        if (!text || typeof text !== 'string' || !text.trim()) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Input text is required and must be a non-empty string.' }),
            };
        }

        // Validate style parameters if provided
        if (styleStrength !== undefined && (typeof styleStrength !== 'number' || styleStrength < 0 || styleStrength > 100)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Style strength must be a number between 0 and 100.' }),
            };
        }

        let modifiedText = text; // Start with the original text

        const startTime = Date.now();

        // --- Step 1: Enhanced AI Paraphrasing with Multiple Providers ---
        console.log("Starting enhanced AI paraphrasing...");
        const paraphraseResult = await paraphraseWithPegasus(modifiedText);

        if (paraphraseResult && !paraphraseResult.error && typeof paraphraseResult.paraphrased_text === 'string') {
            modifiedText = paraphraseResult.paraphrased_text;
            const processingTime = Date.now() - startTime;
            console.log(`Successfully paraphrased with ${paraphraseResult.provider} in ${processingTime}ms`);
        } else {
            // Fallback to local paraphrasing when all AI services fail
            console.warn(`AI paraphrasing failed: ${paraphraseResult?.message}. Using enhanced local fallback.`);
            console.log("Applying enhanced local paraphrasing...");
            modifiedText = await simpleParaphrase(modifiedText);
            console.log("Enhanced local paraphrasing completed.");
        }

        // --- Step 2: Apply balanced humanization with optional style ---
        console.log("Applying balanced humanization...");
        if (styleProfile && styleStrength > 0) {
            console.log(`Applying personal style: ${styleProfile.name} at ${styleStrength}% strength`);
            // The balancedHumanization function now handles style integration
            const result = await balancedHumanization(modifiedText, styleProfile, styleStrength);
            modifiedText = typeof result === 'string' ? result : await result;
        } else {
            modifiedText = balancedHumanization(modifiedText);
        }

        // --- Step 3: Quality check ---
        console.log("Performing quality check...");
        const qualityResult = qualityCheck(modifiedText);

        if (qualityResult.hasIssues) {
            console.log("Quality issues detected:", qualityResult.issues);
            // Apply minimal additional processing if needed
            modifiedText = addControlledMistakes(modifiedText);
        } else {
            console.log("Quality check passed");
        }

        // --- Step 4: Apply subtle style changes ---
        console.log("Applying subtle style changes...");
        modifiedText = changeStyle(modifiedText);

        // --- Step 5: AI Detection Check ---
        console.log("Performing AI detection check...");
        const detectionResult = await checkWithGPTZero(modifiedText);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ modifiedText, detectionResult }),
        };

    } catch (error) {
        console.error("Error in /api/process:", error);
        const errorMessage = error.message || 'Error processing text.';
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                message: errorMessage,
                error: error.toString(),
                detectionResult: {
                    error: true,
                    status: "Server Error",
                    message: "Failed to process text due to an internal server error.",
                    score: null
                }
            }),
        };
    }
};
