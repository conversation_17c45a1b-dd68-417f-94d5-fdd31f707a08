{"name": "stealthwriter-ai", "version": "1.0.0", "description": "AI text modification tool to make AI-generated content less detectable", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "prisma generate && next build", "build:vercel": "prisma generate && next build", "build:netlify": "cross-env NETLIFY=true next build", "export": "next export", "build:static": "next build && next export", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "node prisma/seed.js", "postinstall": "prisma generate", "prepare": "husky install", "vercel-build": "prisma generate && prisma migrate deploy && next build"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.7.1", "@stripe/stripe-js": "^2.4.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "clsx": "^2.0.0", "cors": "^2.8.5", "date-fns": "^3.0.6", "dotenv": "^16.3.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "micro": "^10.0.1", "next": "^14.0.4", "next-auth": "^4.24.5", "nodemailer": "^6.9.7", "prisma": "^5.7.1", "rate-limiter-flexible": "^4.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "stripe": "^14.9.0", "uuid": "^9.0.1", "winston": "^3.11.0", "yup": "^1.4.0"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "keywords": ["ai", "text-modification", "paraphrasing", "ai-detection", "nextjs", "react", "typescript"], "author": "<PERSON>", "license": "Creative Commons Attribution-NonCommercial 4.0 International Public License", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/stealthwriter-ai.git"}, "bugs": {"url": "https://github.com/HectorTa1989/stealthwriter-ai/issues"}, "homepage": "https://github.com/HectorTa1989/stealthwriter-ai#readme"}