{"imports": {"zlib": "node:zlib", "worker_threads": "node:worker_threads", "wasi": "node:wasi", "vm": "node:vm", "v8": "node:v8", "util/types": "node:util/types", "util": "node:util", "url": "node:url", "tty": "node:tty", "trace_events": "node:trace_events", "tls": "node:tls", "timers/promises": "node:timers/promises", "timers": "node:timers", "sys": "node:sys", "string_decoder": "node:string_decoder", "stream/web": "node:stream/web", "stream/promises": "node:stream/promises", "stream/consumers": "node:stream/consumers", "stream": "node:stream", "repl": "node:repl", "readline/promises": "node:readline/promises", "readline": "node:readline", "querystring": "node:querystring", "punycode": "node:punycode", "process": "node:process", "perf_hooks": "node:perf_hooks", "path/win32": "node:path/win32", "path/posix": "node:path/posix", "path": "node:path", "os": "node:os", "net": "node:net", "module": "node:module", "inspector/promises": "node:inspector/promises", "inspector": "node:inspector", "https": "node:https", "http2": "node:http2", "http": "node:http", "fs/promises": "node:fs/promises", "fs": "node:fs", "events": "node:events", "domain": "node:domain", "dns/promises": "node:dns/promises", "dns": "node:dns", "diagnostics_channel": "node:diagnostics_channel", "dgram": "node:dgram", "crypto": "node:crypto", "constants": "node:constants", "console": "node:console", "cluster": "node:cluster", "child_process": "node:child_process", "buffer": "node:buffer", "async_hooks": "node:async_hooks", "assert/strict": "node:assert/strict", "assert": "node:assert", "_tls_wrap": "node:_tls_wrap", "_tls_common": "node:_tls_common", "_stream_writable": "node:_stream_writable", "_stream_wrap": "node:_stream_wrap", "_stream_transform": "node:_stream_transform", "_stream_readable": "node:_stream_readable", "_stream_passthrough": "node:_stream_passthrough", "_stream_duplex": "node:_stream_duplex", "_http_server": "node:_http_server", "_http_outgoing": "node:_http_outgoing", "_http_incoming": "node:_http_incoming", "_http_common": "node:_http_common", "_http_client": "node:_http_client", "_http_agent": "node:_http_agent", "@netlify/edge-functions": "https://edge.netlify.com/v1/index.ts", "netlify:edge": "https://edge.netlify.com/v1/index.ts?v=legacy"}, "scopes": {}}