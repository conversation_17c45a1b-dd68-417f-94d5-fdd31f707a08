// Import other utilities and the GPTZero client
import { addControlledMistakes, changeStyle, simpleParaphrase } from '../../src/utils/textModifiers';
import { checkWithGPTZero } from '../../src/services/gptzeroClient';
import { paraphraseWithPegasus } from '../../src/services/paraphraseService';
import { balancedHumanization, qualityCheck } from '../../src/utils/balancedHumanizer';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const { text, styleProfile, styleStrength } = req.body;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return res.status(400).json({ message: 'Input text is required and must be a non-empty string.' });
    }

    // Validate style parameters if provided
    if (styleStrength !== undefined && (typeof styleStrength !== 'number' || styleStrength < 0 || styleStrength > 100)) {
        return res.status(400).json({ message: 'Style strength must be a number between 0 and 100.' });
    }

    let modifiedText = text; // Start with the original text

    try {
        const startTime = Date.now();

        // --- Step 1: Enhanced AI Paraphrasing with Multiple Providers ---
        console.log("Starting enhanced AI paraphrasing...");
        const paraphraseResult = await paraphraseWithPegasus(modifiedText);

        if (paraphraseResult && !paraphraseResult.error && typeof paraphraseResult.paraphrased_text === 'string') {
            modifiedText = paraphraseResult.paraphrased_text;
            const processingTime = Date.now() - startTime;
            console.log(`Successfully paraphrased with ${paraphraseResult.provider} in ${processingTime}ms`);
        } else {
            // Fallback to local paraphrasing when all AI services fail
            console.warn(`AI paraphrasing failed: ${paraphraseResult?.message}. Using enhanced local fallback.`);
            console.log("Applying enhanced local paraphrasing...");
            modifiedText = await simpleParaphrase(modifiedText);
            console.log("Enhanced local paraphrasing completed.");
        }

        // --- Step 2: Apply balanced humanization with optional style ---
        console.log("Applying balanced humanization...");
        if (styleProfile && styleStrength > 0) {
            console.log(`Applying personal style: ${styleProfile.name} at ${styleStrength}% strength`);
            // The balancedHumanization function now handles style integration
            const result = await balancedHumanization(modifiedText, styleProfile, styleStrength);
            modifiedText = typeof result === 'string' ? result : await result;
        } else {
            modifiedText = balancedHumanization(modifiedText);
        }

        // --- Step 3: Quality check ---
        console.log("Performing quality check...");
        const qualityResult = qualityCheck(modifiedText);

        if (qualityResult.hasIssues) {
            console.log("Quality issues detected:", qualityResult.issues);
            // Apply minimal additional processing if needed
            modifiedText = addControlledMistakes(modifiedText);
        } else {
            console.log("Quality check passed");
        }

        // --- Step 4: Apply subtle style changes ---
        console.log("Applying subtle style changes...");
        modifiedText = changeStyle(modifiedText);

        // --- Step 5: AI Detection Check ---
        console.log("Performing AI detection check...");
        const detectionResult = await checkWithGPTZero(modifiedText);

        res.status(200).json({ modifiedText, detectionResult });

    } catch (error) {
        // This catches errors from textModifiers or other unexpected issues within this handler
        console.error("Error in /api/process main try block:", error);
        const errorMessage = error.message || 'Error processing text.';
        res.status(500).json({
            message: errorMessage,
            error: error.toString(),
            detectionResult: { // Ensure detectionResult has a consistent error structure
                error: true,
                status: "Server Error",
                message: "Failed to process text due to an internal server error in the API handler.",
                score: null
            }
        });
    }
}
