(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[816],{2336:function(e,r,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/client-protected",function(){return s(7531)}])},7531:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return m}});var t=s(5893),i=s(3299),n=s(1163),o=s(6993),a=s(7294),l=s(5202),c=s.n(l),d=s(614),u=s.n(d);function m(){let{data:e,status:r}=(0,i.useSession)({required:!0,onUnauthenticated(){console.log("User is not authenticated on ClientProtectedPage. NextAuth will handle redirect.")}}),s=(0,n.useRouter)(),[l,d]=(0,a.useState)(null),[m,p]=(0,a.useState)(null),[h,g]=(0,a.useState)(!1),[x,f]=(0,a.useState)(""),[j,b]=(0,a.useState)(!1),[y,N]=(0,a.useState)("");(0,a.useEffect)(()=>{var r;(null==e?void 0:null===(r=e.user)||void 0===r?void 0:r.id)&&!l&&!h&&(g(!0),p(null),fetch("/api/user-profile").then(async e=>{if(!e.ok){let r;try{r=await e.json()}catch(s){r={message:"Error ".concat(e.status,": Failed to fetch profile details.")}}throw Error(r.message||r.error||"Error ".concat(e.status))}return e.json()}).then(e=>{d(e)}).catch(e=>{console.error("Failed to fetch user profile:",e),p(e.message),d(null)}).finally(()=>{g(!1)}))},[e,l,h]);let C=async()=>{f("Attempting to access premium feature...");try{let e=await fetch("/api/premium-feature"),r=await e.json();if(!e.ok)throw Error(r.message||r.error||"Error ".concat(e.status));f("Premium Feature Response: ".concat(r.message," - Data: ").concat(r.data))}catch(e){console.error("Premium feature access error:",e),f("Error: ".concat(e.message))}},w=async()=>{b(!0),N("");try{let e=await fetch("/api/stripe/create-customer-portal-session",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok){let r=await e.json().catch(()=>({message:"Failed to create customer portal session. Please try again."}));throw Error(r.message||r.error||"Error: ".concat(e.status))}let{url:r}=await e.json();if(r)window.location.href=r;else throw Error("Customer portal URL not received from server.")}catch(e){console.error("Manage subscription error:",e.message),N("Could not open subscription management: ".concat(e.message))}finally{b(!1)}};return"loading"===r||e&&h&&!l&&!m?(0,t.jsx)(o.Z,{children:(0,t.jsx)("div",{className:c().container,children:(0,t.jsx)("p",{className:c().loadingMessage,children:"Loading session or profile..."})})}):e?(0,t.jsx)(o.Z,{children:(0,t.jsxs)("div",{className:c().container,children:[(0,t.jsx)("h1",{className:c().title,children:"User Dashboard"})," ",(0,t.jsxs)("div",{className:c().protectedContent,style:{marginBottom:"20px",textAlign:"left"},children:[(0,t.jsxs)("h3",{children:["Welcome, ",e.user.name||e.user.email,"!"]}),(0,t.jsx)("p",{children:"This is your protected area. Your profile information from our database is shown below."})]}),m&&(0,t.jsxs)("div",{className:c().error,style:{marginBottom:"20px"},children:[(0,t.jsxs)("p",{children:["Error loading your full profile: ",m]}),(0,t.jsx)("p",{children:"Some features might be unavailable. Please try refreshing the page."})]}),l?(0,t.jsxs)("div",{className:u().planCard,style:{marginBottom:"20px",textAlign:"left",border:"2px solid ".concat("premium"===l.subscriptionTier?"#28a745":"#0070f3")},children:[" ",(0,t.jsx)("h4",{className:u().planTitle,style:{fontSize:"1.5rem"},children:"Your Profile Details:"}),(0,t.jsxs)("ul",{className:u().featuresList,style:{fontSize:"1rem"},children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"ID:"})," ",l.id]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Email:"})," ",l.email]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Name:"})," ",l.name||"N/A"]}),(0,t.jsxs)("li",{style:{color:"premium"===l.subscriptionTier?"#28a745":"inherit"},children:[(0,t.jsx)("strong",{children:"Subscription Tier:"})," ",(0,t.jsx)("span",{style:{fontWeight:"bold"},children:l.subscriptionTier})]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Usage Credits:"})," ",l.usageCredits]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Member Since:"})," ",new Date(l.createdAt).toLocaleDateString()]})]}),"premium"===l.subscriptionTier&&(0,t.jsx)("button",{onClick:w,disabled:j,className:u().planButton,style:{marginTop:"15px"},children:j?"Processing...":"Manage Subscription"}),y&&(0,t.jsx)("p",{style:{color:"red",marginTop:"10px",fontSize:"0.9rem"},children:y})]}):!m&&(0,t.jsx)("p",{className:c().loadingMessage,children:"Loading your extended profile details..."}),l&&"premium"===l.subscriptionTier&&(0,t.jsxs)("div",{className:c().protectedContent,style:{borderColor:"green",backgroundColor:"#e6ffe6",marginBottom:"20px"},children:[(0,t.jsx)("h4",{children:"Premium Features Access"}),(0,t.jsx)("p",{children:"As a premium user, you can access exclusive API endpoints."}),(0,t.jsx)("button",{onClick:C,className:u().planButton,style:{backgroundColor:"#5cb85c",borderColor:"#4cae4c"},children:"Test Premium API Endpoint"})]}),l&&"premium"!==l.subscriptionTier&&(0,t.jsxs)("div",{className:c().signInPrompt,style:{borderColor:"orange",backgroundColor:"#fff8e1",marginBottom:"20px"},children:[(0,t.jsx)("h4",{children:"Upgrade to Premium"}),(0,t.jsx)("p",{children:"Unlock more features, higher limits, and an ad-free experience by upgrading to Premium."}),(0,t.jsx)("button",{onClick:()=>s.push("/pricing"),className:u().planButton,style:{backgroundColor:"orange"},children:"View Pricing / Upgrade"}),(0,t.jsx)("br",{}),(0,t.jsx)("button",{onClick:C,className:u().planButton,style:{backgroundColor:"grey",marginTop:"10px"},children:"(Test Premium API - Expect Denied)"})]}),x&&(0,t.jsx)("p",{className:c().loadingMessage,style:{fontSize:"0.9rem",padding:"10px",backgroundColor:"#f9f9f9",border:"1px solid #eee",marginTop:"10px"},children:x}),(0,t.jsxs)("div",{style:{marginTop:"30px",textAlign:"left",backgroundColor:"#f9f9f9",padding:"15px",borderRadius:"8px"},children:[(0,t.jsx)("h4",{children:"Raw Session Object (Client-Side):"}),(0,t.jsx)("pre",{style:{backgroundColor:"#f0f0f0",padding:"15px",borderRadius:"5px",overflowX:"auto",fontSize:"0.85rem"},children:JSON.stringify(e,null,2)})]})]})}):(0,t.jsx)(o.Z,{children:(0,t.jsx)("div",{className:c().container,children:(0,t.jsx)("p",{className:c().signInPrompt,children:"You are not signed in. Redirecting..."})})})}}},function(e){e.O(0,[351,888,774,179],function(){return e(e.s=2336)}),_N_E=e.O()}]);