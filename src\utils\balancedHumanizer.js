/**
 * Balanced Humanization - Achieves low AI detection while maintaining quality
 * Now supports personal writing style integration
 */

/**
 * Main balanced humanization function
 * Focuses on subtle changes that maintain readability
 * @param {string} text - Text to humanize
 * @param {Object} styleProfile - Optional user style profile
 * @param {number} styleStrength - Style strength (0-100)
 */
export function balancedHumanization(text, styleProfile = null, styleStrength = 0) {
    // If style profile is provided, use the integrated style application
    if (styleProfile && styleStrength > 0) {
        // Import dynamically to avoid circular dependency
        return import('../services/styleApplicationService.js').then(module => {
            return module.applyWritingStyle(text, styleProfile, styleStrength);
        });
    }

    // Otherwise, use the original balanced humanization
    let result = text;
    
    // Step 1: Replace AI-typical words with natural alternatives
    result = replaceAIWords(result);
    
    // Step 2: Add subtle sentence variety
    result = addSubtleSentenceVariety(result);
    
    // Step 3: Include natural contractions
    result = addNaturalContractions(result);
    
    // Step 4: Add minimal personal touches (sparingly)
    result = addMinimalPersonalTouches(result);
    
    // Step 5: Fix AI-typical patterns without breaking grammar
    result = fixAIPatterns(result);
    
    return result;
}

/**
 * Replace AI-typical words with more natural alternatives
 */
function replaceAIWords(text) {
    const replacements = {
        // AI loves these words - replace with simpler alternatives
        'utilize': 'use',
        'leverage': 'use',
        'facilitate': 'help',
        'implement': 'put in place',
        'optimize': 'improve',
        'enhance': 'improve',
        'comprehensive': 'complete',
        'robust': 'strong',
        'seamless': 'smooth',
        'innovative': 'new',
        'cutting-edge': 'latest',
        'state-of-the-art': 'advanced',
        'paradigm': 'approach',
        'methodology': 'method',
        'framework': 'structure',
        'ecosystem': 'system',
        'landscape': 'field',
        'realm': 'area',
        'sphere': 'area',
        'domain': 'field',
        'facet': 'aspect',
        'dimension': 'aspect',
        'component': 'part',
        'element': 'part',
        'factor': 'thing',
        'aspect': 'part',
        'notion': 'idea',
        'concept': 'idea',
        'principle': 'rule',
        'fundamental': 'basic',
        'essential': 'important',
        'crucial': 'important',
        'vital': 'important',
        'significant': 'important',
        'substantial': 'large',
        'considerable': 'large',
        'numerous': 'many',
        'various': 'different',
        'diverse': 'different',
        'multiple': 'many',
        'plethora': 'many',
        'myriad': 'many',
        'abundance': 'lots',
        'multitude': 'many',

        // Additional AI patterns that were missing
        'revolutionized': 'changed',
        'sophisticated': 'advanced',
        'effectively': 'well',
        'undoubtedly': 'clearly',
        'demonstrate': 'show',
        'transformative': 'big',
        'advancement': 'progress',
        'technological': 'tech',
        'implications': 'effects',
        'stakeholders': 'people involved',
        'artificial intelligence': 'AI',
        'machine learning': 'ML',
        'neural networks': 'AI networks',
        'algorithms': 'programs',
        'organizations': 'companies',
        'capabilities': 'abilities',
        'operations': 'work',
        'processes': 'steps',
        'integration': 'adding',
        'represents': 'is',
        'approach': 'handle',
        'challenges': 'problems',
        'solutions': 'answers',
        'applications': 'uses',
        'evolution': 'change',
        'consideration': 'thinking about',
        'collaborate': 'work together',
        'benefits': 'helps'
    };

    let result = text;
    
    Object.entries(replacements).forEach(([aiWord, naturalWord]) => {
        // Only replace 70% of occurrences to avoid being too obvious
        const regex = new RegExp(`\\b${aiWord}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.15) { // 85% chance to replace
                return match === match.toLowerCase() ? naturalWord : 
                       match === match.toUpperCase() ? naturalWord.toUpperCase() :
                       naturalWord.charAt(0).toUpperCase() + naturalWord.slice(1);
            }
            return match;
        });
    });

    return result;
}

/**
 * Add subtle sentence variety without breaking grammar
 * Preserves paragraph breaks and original formatting
 */
function addSubtleSentenceVariety(text) {
    let result = text;

    // Split by paragraphs first to preserve paragraph structure
    const paragraphs = result.split(/\n\s*\n/);

    const processedParagraphs = paragraphs.map(paragraph => {
        if (!paragraph.trim()) return paragraph;

        const sentences = paragraph.split(/(?<=[.!?])\s+/);

        const variedSentences = sentences.map((sentence, index) => {
            // Occasionally start sentences differently
            if (Math.random() > 0.8 && sentence.length > 30) {
                // Add subtle sentence starters (sparingly)
                const starters = ['Actually, ', 'Basically, ', 'Generally, ', 'Typically, '];
                if (Math.random() > 0.7) {
                    const starter = starters[Math.floor(Math.random() * starters.length)];
                    sentence = starter + sentence.charAt(0).toLowerCase() + sentence.slice(1);
                }
            }

            // Occasionally combine very short sentences
            if (sentence.length < 20 && index < sentences.length - 1 && sentences[index + 1].length < 25) {
                const nextSentence = sentences[index + 1];
                sentence = sentence.replace(/\.$/, '') + ', and ' +
                          nextSentence.charAt(0).toLowerCase() + nextSentence.slice(1);
                sentences[index + 1] = ''; // Mark for removal
            }

            return sentence;
        }).filter(s => s !== ''); // Remove empty sentences

        return variedSentences.join(' ');
    });

    return processedParagraphs.join('\n\n');
}

/**
 * Add natural contractions that humans use
 */
function addNaturalContractions(text) {
    const contractions = {
        'do not': "don't",
        'does not': "doesn't", 
        'did not': "didn't",
        'will not': "won't",
        'would not': "wouldn't",
        'could not': "couldn't",
        'should not': "shouldn't",
        'cannot': "can't",
        'is not': "isn't",
        'are not': "aren't",
        'was not': "wasn't",
        'were not': "weren't",
        'have not': "haven't",
        'has not': "hasn't",
        'had not': "hadn't",
        'it is': "it's",
        'that is': "that's",
        'there is': "there's",
        'you are': "you're",
        'we are': "we're",
        'they are': "they're",
        'I will': "I'll",
        'you will': "you'll",
        'we will': "we'll",
        'they will': "they'll"
    };

    let result = text;
    
    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            // Contract 80% of the time
            if (Math.random() > 0.2) {
                return match === match.toLowerCase() ? contracted : 
                       match === match.toUpperCase() ? contracted.toUpperCase() :
                       contracted.charAt(0).toUpperCase() + contracted.slice(1);
            }
            return match;
        });
    });

    return result;
}

/**
 * Add minimal personal touches without making it silly
 */
function addMinimalPersonalTouches(text) {
    let result = text;
    
    // Very sparingly add subtle personal elements
    if (Math.random() > 0.9) {
        result = result.replace(/\b(This|That)\s+(is|was|will be)\s+(important|significant|crucial)/gi, 
            (match) => {
                if (Math.random() > 0.8) {
                    return match.replace(/(important|significant|crucial)/gi, 'really important');
                }
                return match;
            });
    }
    
    // Occasionally soften absolute statements
    result = result.replace(/\b(always|never|all|every|completely|totally)\b/gi, (match) => {
        if (Math.random() > 0.85) {
            const softer = {
                'always': 'usually',
                'never': 'rarely',
                'all': 'most',
                'every': 'most',
                'completely': 'mostly',
                'totally': 'mostly'
            };
            const replacement = softer[match.toLowerCase()];
            if (replacement) {
                return match === match.toLowerCase() ? replacement : 
                       match === match.toUpperCase() ? replacement.toUpperCase() :
                       replacement.charAt(0).toUpperCase() + replacement.slice(1);
            }
        }
        return match;
    });
    
    return result;
}

/**
 * Fix AI-typical patterns while maintaining good grammar
 */
function fixAIPatterns(text) {
    let result = text;
    
    // Remove AI-typical opening phrases
    const aiOpenings = [
        /^In conclusion,?\s*/i,
        /^To summarize,?\s*/i,
        /^In summary,?\s*/i,
        /^Overall,?\s*/i,
        /^It is important to note that\s*/i,
        /^It should be noted that\s*/i,
        /^It is worth mentioning that\s*/i,
        /^It is essential to understand that\s*/i
    ];

    aiOpenings.forEach(pattern => {
        result = result.replace(pattern, '');
    });
    
    // Replace AI-typical transition phrases with more natural ones
    const transitions = {
        'Furthermore,': 'Also,',
        'Moreover,': 'Plus,',
        'Additionally,': 'Also,',
        'In addition,': 'Also,',
        'Nevertheless,': 'But',
        'Nonetheless,': 'Still,',
        'Consequently,': 'So',
        'Therefore,': 'So',
        'Thus,': 'So',
        'Hence,': 'So',
        'Accordingly,': 'So'
    };
    
    Object.entries(transitions).forEach(([formal, casual]) => {
        // Only replace some occurrences to maintain some variety
        const regex = new RegExp(`\\b${formal}`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.5) { // 50% chance to replace
                return casual;
            }
            return match;
        });
    });
    
    // Fix passive voice to active voice (humans prefer active)
    result = result.replace(/\bis\s+(\w+ed)\s+by\s+/gi, (match, verb) => {
        if (Math.random() > 0.7) {
            // This is a simplified conversion - in practice, this would need more context
            return 'uses ';
        }
        return match;
    });
    
    // Break up overly long sentences (AI tendency) while preserving paragraphs
    const paragraphs = result.split(/\n\s*\n/);
    const processedParagraphs = paragraphs.map(paragraph => {
        if (!paragraph.trim()) return paragraph;

        const sentences = paragraph.split(/(?<=[.!?])\s+/);
        const improvedSentences = sentences.map(sentence => {
            if (sentence.length > 120 && sentence.includes(',')) {
                // Find a good breaking point
                const commaIndex = sentence.indexOf(',', 60);
                if (commaIndex > 0 && commaIndex < sentence.length - 20) {
                    const firstPart = sentence.substring(0, commaIndex).trim() + '.';
                    const secondPart = sentence.substring(commaIndex + 1).trim();
                    return firstPart + ' ' + secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                }
            }
            return sentence;
        });

        return improvedSentences.join(' ');
    });

    return processedParagraphs.join('\n\n');
}

/**
 * Quality check - ensures the text maintains readability
 */
export function qualityCheck(text) {
    // Check for basic readability issues
    const issues = [];
    
    // Check for too many consecutive short sentences
    const sentences = text.split(/(?<=[.!?])\s+/);
    let shortSentenceCount = 0;
    
    for (let i = 0; i < sentences.length; i++) {
        if (sentences[i].length < 15) {
            shortSentenceCount++;
            if (shortSentenceCount > 2) {
                issues.push('Too many consecutive short sentences');
                break;
            }
        } else {
            shortSentenceCount = 0;
        }
    }
    
    // Check for grammar issues (basic)
    if (text.includes('..')) {
        issues.push('Multiple periods found');
    }
    
    if (text.includes('  ')) {
        issues.push('Multiple spaces found');
    }
    
    return {
        hasIssues: issues.length > 0,
        issues: issues,
        text: text
    };
}
